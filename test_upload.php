<?php
session_start();
require_once 'config/database.php';

$message = '';
$error = '';

// 处理测试上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_FILES['test_file'])) {
    $file = $_FILES['test_file'];
    
    echo "<h2>上传测试结果</h2>";
    echo "<p><strong>文件信息：</strong></p>";
    echo "<ul>";
    echo "<li>文件名: " . htmlspecialchars($file['name']) . "</li>";
    echo "<li>文件类型: " . htmlspecialchars($file['type']) . "</li>";
    echo "<li>文件大小: " . number_format($file['size'] / 1024 / 1024, 2) . " MB</li>";
    echo "<li>错误代码: " . $file['error'] . "</li>";
    echo "</ul>";
    
    // 检查错误代码
    switch ($file['error']) {
        case UPLOAD_ERR_OK:
            echo "<p class='success'>✅ 文件上传成功，没有错误</p>";
            break;
        case UPLOAD_ERR_INI_SIZE:
            echo "<p class='error'>❌ 文件大小超过了 php.ini 中 upload_max_filesize 的限制</p>";
            break;
        case UPLOAD_ERR_FORM_SIZE:
            echo "<p class='error'>❌ 文件大小超过了 HTML 表单中 MAX_FILE_SIZE 的限制</p>";
            break;
        case UPLOAD_ERR_PARTIAL:
            echo "<p class='error'>❌ 文件只有部分被上传</p>";
            break;
        case UPLOAD_ERR_NO_FILE:
            echo "<p class='error'>❌ 没有文件被上传</p>";
            break;
        case UPLOAD_ERR_NO_TMP_DIR:
            echo "<p class='error'>❌ 找不到临时文件夹</p>";
            break;
        case UPLOAD_ERR_CANT_WRITE:
            echo "<p class='error'>❌ 文件写入失败</p>";
            break;
        case UPLOAD_ERR_EXTENSION:
            echo "<p class='error'>❌ 文件上传被扩展程序阻止</p>";
            break;
        default:
            echo "<p class='error'>❌ 未知错误</p>";
            break;
    }
    
    // 显示PHP配置信息
    echo "<h3>PHP配置信息：</h3>";
    echo "<ul>";
    echo "<li>upload_max_filesize: " . ini_get('upload_max_filesize') . "</li>";
    echo "<li>post_max_size: " . ini_get('post_max_size') . "</li>";
    echo "<li>max_execution_time: " . ini_get('max_execution_time') . " 秒</li>";
    echo "<li>max_input_time: " . ini_get('max_input_time') . " 秒</li>";
    echo "<li>memory_limit: " . ini_get('memory_limit') . "</li>";
    echo "</ul>";
    
    if ($file['error'] === UPLOAD_ERR_OK) {
        // 如果上传成功，尝试移动文件
        $upload_dir = 'uploads/test/';
        if (!is_dir($upload_dir)) {
            mkdir($upload_dir, 0755, true);
        }
        
        $filename = 'test_' . time() . '_' . $file['name'];
        $upload_path = $upload_dir . $filename;
        
        if (move_uploaded_file($file['tmp_name'], $upload_path)) {
            echo "<p class='success'>✅ 文件成功保存到: {$upload_path}</p>";
            echo "<p><a href='{$upload_path}' target='_blank'>查看文件</a></p>";
        } else {
            echo "<p class='error'>❌ 文件移动失败</p>";
        }
    }
    
    echo "<hr>";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>文件上传测试 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            line-height: 1.6;
        }
        .success {
            color: #28a745;
            font-weight: bold;
        }
        .error {
            color: #dc3545;
            font-weight: bold;
        }
        .info {
            background: #e7f3ff;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .card {
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            margin: 20px 0;
        }
    </style>
</head>
<body>
    <h1>📁 文件上传测试工具</h1>
    
    <div class="info">
        <h3>🔧 修复说明</h3>
        <p>已修复的问题：</p>
        <ul>
            <li>✅ 将 .htaccess 中的文件上传限制设置为 50MB</li>
            <li>✅ 将 orders.php 中的文件大小检查从 10MB 更新为 50MB</li>
            <li>✅ 更新了前端提示信息</li>
        </ul>
    </div>
    
    <div class="card">
        <h2>📤 测试文件上传</h2>
        <p>请选择一个PDF文件进行上传测试（建议测试不同大小的文件）：</p>
        
        <form method="POST" enctype="multipart/form-data">
            <div class="mb-3">
                <label for="test_file" class="form-label">选择PDF文件：</label>
                <input type="file" class="form-control" id="test_file" name="test_file" accept=".pdf" required>
                <div class="form-text">支持最大50MB的PDF文件</div>
            </div>
            <button type="submit" class="btn btn-primary">
                <i class="fas fa-upload"></i> 开始测试上传
            </button>
        </form>
    </div>
    
    <div class="card">
        <h3>📊 当前PHP配置</h3>
        <ul>
            <li><strong>upload_max_filesize:</strong> <?php echo ini_get('upload_max_filesize'); ?></li>
            <li><strong>post_max_size:</strong> <?php echo ini_get('post_max_size'); ?></li>
            <li><strong>max_execution_time:</strong> <?php echo ini_get('max_execution_time'); ?> 秒</li>
            <li><strong>max_input_time:</strong> <?php echo ini_get('max_input_time'); ?> 秒</li>
            <li><strong>memory_limit:</strong> <?php echo ini_get('memory_limit'); ?></li>
        </ul>
    </div>
    
    <div class="card">
        <h3>🔗 相关链接</h3>
        <p>
            <a href="admin/orders.php" class="btn btn-success me-2">📋 订单管理页面</a>
            <a href="admin/dashboard.php" class="btn btn-info me-2">⚙️ 管理后台</a>
            <a href="index.php" class="btn btn-secondary">🏠 返回主页</a>
        </p>
    </div>
    
    <div class="card">
        <h3>💡 测试建议</h3>
        <ol>
            <li>先测试一个小文件（1-5MB）确保基本功能正常</li>
            <li>然后测试一个中等大小的文件（10-20MB）</li>
            <li>最后测试一个接近限制的文件（40-50MB）</li>
            <li>如果仍有问题，检查服务器错误日志</li>
        </ol>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>