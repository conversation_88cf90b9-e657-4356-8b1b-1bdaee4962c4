<?php
session_start();
require_once '../config/database.php';

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$success_message = '';
$error_message = '';

// 处理上传报告
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'upload_report') {
    $order_id = $_POST['order_id'] ?? '';
    $real_name = $_POST['real_name'] ?? '';
    $user_phone = $_POST['user_phone'] ?? '';
    
    if (!empty($order_id) && !empty($real_name) && isset($_FILES['pdf_file'])) {
        $file = $_FILES['pdf_file'];
        
        // 验证文件类型
        if ($file['type'] !== 'application/pdf') {
            $error_message = '只能上传PDF文件';
        } else if ($file['size'] > 50 * 1024 * 1024) { // 50MB限制
            $error_message = '文件大小不能超过50MB';
        } else {
            $database = new Database();
            $conn = $database->getConnection();
            
            // 检查订单号是否已存在报告
            $stmt = $conn->prepare("SELECT id FROM reports WHERE order_number = ?");
            $stmt->execute([$order_id]);
            if ($stmt->fetch()) {
                $error_message = '该订单已存在报告，请先删除原报告';
            } else {
                // 生成唯一文件名
                $file_extension = '.pdf';
                $unique_filename = uniqid() . '_' . time() . $file_extension;
                $upload_path = '../uploads/reports/' . $unique_filename;
                
                if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                    // 插入数据库
                    $stmt = $conn->prepare("INSERT INTO reports (name, phone, order_number, pdf_path, upload_time) VALUES (?, ?, ?, ?, ?)");
                    if ($stmt->execute([$real_name, $user_phone, $order_id, 'uploads/reports/' . $unique_filename, time()])) {
                        $success_message = '报告上传成功！';
                    } else {
                        $error_message = '数据库保存失败';
                        unlink($upload_path); // 删除已上传的文件
                    }
                } else {
                    $error_message = '文件上传失败';
                }
            }
        }
    } else {
        $error_message = '请填写完整信息并选择PDF文件';
    }
}

// 分页参数
$page = isset($_GET['page']) ? max(1, intval($_GET['page'])) : 1;
$per_page = 20;
$offset = ($page - 1) * $per_page;

// 搜索参数
$search = $_GET['search'] ?? '';
$search_condition = '';
$search_params = [];

if (!empty($search)) {
    $search_condition = " AND (order_id LIKE ? OR real_name LIKE ? OR user_phone LIKE ?)";
    $search_params = ["%$search%", "%$search%", "%$search%"];
}

$database = new Database();
$conn = $database->getConnection();

// 获取订单总数
$count_sql = "SELECT COUNT(*) FROM eb_store_order WHERE is_del = 0 AND is_cancel = 0" . $search_condition;
$count_stmt = $conn->prepare($count_sql);
$count_stmt->execute($search_params);
$total_orders = $count_stmt->fetchColumn();
$total_pages = ceil($total_orders / $per_page);

// 获取订单列表（包含报告状态）
$sql = "SELECT o.id, o.order_id, o.real_name, o.user_phone, o.total_price, o.pay_price, o.paid, o.status, o.add_time, o.pay_time,
               r.id as report_id, r.pdf_path, r.upload_time
        FROM eb_store_order o
        LEFT JOIN reports r ON o.order_id = r.order_number
        WHERE o.is_del = 0 AND o.is_cancel = 0" . $search_condition . "
        ORDER BY o.add_time DESC 
        LIMIT $per_page OFFSET $offset";
$stmt = $conn->prepare($sql);
$stmt->execute($search_params);
$orders = $stmt->fetchAll();

// 订单状态映射
$status_map = [
    -2 => '退货成功',
    -1 => '已退款',
    0 => '待发货',
    1 => '待收货',
    2 => '已收货',
    3 => '待评价'
];
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>订单管理 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table th {
            background: #f8f9fa;
            border: none;
            font-weight: 600;
        }
        .table td {
            border: none;
            border-bottom: 1px solid #e9ecef;
            vertical-align: middle;
        }
        .badge {
            font-size: 0.75em;
        }
        .pagination .page-link {
            border-radius: 8px;
            margin: 0 2px;
            border: none;
            color: #667eea;
        }
        .pagination .page-item.active .page-link {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
        }
        .search-box {
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .search-box:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .modal-content {
            border-radius: 15px;
            border: none;
        }
        .modal-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.php">
                <i class="fas fa-file-alt me-2"></i>
                报告管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    <?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                </span>
                <a class="nav-link" href="dashboard.php">
                    <i class="fas fa-tachometer-alt me-1"></i>
                    报告管理
                </a>
                <a class="nav-link" href="change_password.php">
                    <i class="fas fa-key me-1"></i>
                    修改密码
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    退出登录
                </a>
            </div>
        </div>
    </nav>

    <div class="container-fluid mt-4">
        <!-- 消息提示 -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="card">
            <div class="card-header">
                <div class="row align-items-center">
                    <div class="col">
                        <h5 class="mb-0">
                            <i class="fas fa-shopping-cart me-2"></i>
                            订单管理 (共 <?php echo $total_orders; ?> 条)
                        </h5>
                    </div>
                    <div class="col-auto">
                        <form method="GET" class="d-flex">
                            <input type="text" class="form-control search-box me-2" name="search" 
                                   placeholder="搜索订单号/姓名/手机号" value="<?php echo htmlspecialchars($search); ?>" 
                                   style="width: 250px;">
                            <button type="submit" class="btn btn-light">
                                <i class="fas fa-search"></i>
                            </button>
                            <?php if ($search): ?>
                                <a href="orders.php" class="btn btn-outline-light ms-2">
                                    <i class="fas fa-times"></i>
                                </a>
                            <?php endif; ?>
                        </form>
                    </div>
                </div>
            </div>
            <div class="card-body p-0">
                <?php if (empty($orders)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                        <p class="text-muted">暂无订单数据</p>
                    </div>
                <?php else: ?>
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead>
                                <tr>
                                    <th>订单号</th>
                                    <th>客户姓名</th>
                                    <th>手机号</th>
                                    <th>订单金额</th>
                                    <th>实付金额</th>
                                    <th>支付状态</th>
                                    <th>订单状态</th>
                                    <th>报告状态</th>
                                    <th>下单时间</th>
                                    <th>操作</th>
                                </tr>
                            </thead>
                            <tbody>
                                <?php foreach ($orders as $order): ?>
                                    <tr>
                                        <td>
                                            <strong><?php echo htmlspecialchars($order['order_id']); ?></strong>
                                            <?php if ($order['report_id']): ?>
                                                <span class="badge bg-info ms-2">
                                                    <i class="fas fa-file-pdf me-1"></i>已有报告
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo htmlspecialchars($order['real_name']); ?></td>
                                        <td><?php echo htmlspecialchars($order['user_phone']); ?></td>
                                        <td>¥<?php echo number_format($order['total_price'], 2); ?></td>
                                        <td>¥<?php echo number_format($order['pay_price'], 2); ?></td>
                                        <td>
                                            <?php if ($order['paid'] == 1): ?>
                                                <span class="badge bg-success">已支付</span>
                                            <?php else: ?>
                                                <span class="badge bg-warning">未支付</span>
                                            <?php endif; ?>
                                        </td>
                                        <td>
                                            <?php 
                                            switch($order['status']) {
                                                case -2:
                                                case -1:
                                                    $status_class = 'bg-danger';
                                                    break;
                                                case 0:
                                                case 1:
                                                    $status_class = 'bg-warning';
                                                    break;
                                                case 2:
                                                case 3:
                                                    $status_class = 'bg-success';
                                                    break;
                                                default:
                                                    $status_class = 'bg-secondary';
                                                    break;
                                            }
                                            ?>
                                            <span class="badge <?php echo $status_class; ?>">
                                                <?php echo $status_map[$order['status']] ?? '未知状态'; ?>
                                            </span>
                                        </td>
                                        <td>
                                            <?php if ($order['report_id']): ?>
                                                <span class="badge bg-success">
                                                    <i class="fas fa-check me-1"></i>已上传
                                                </span>
                                                <br><small class="text-muted"><?php echo date('Y-m-d H:i', $order['upload_time']); ?></small>
                                            <?php else: ?>
                                                <span class="badge bg-secondary">
                                                    <i class="fas fa-minus me-1"></i>未上传
                                                </span>
                                            <?php endif; ?>
                                        </td>
                                        <td><?php echo date('Y-m-d H:i', $order['add_time']); ?></td>
                                        <td>
                                            <?php if ($order['report_id']): ?>
                                                <a href="../<?php echo htmlspecialchars($order['pdf_path']); ?>" target="_blank" class="btn btn-sm btn-success me-1">
                                                    <i class="fas fa-eye me-1"></i>
                                                    查看报告
                                                </a>
                                                <button type="button" class="btn btn-sm btn-warning" 
                                                        onclick="openUploadModal('<?php echo htmlspecialchars($order['order_id']); ?>', '<?php echo htmlspecialchars($order['real_name']); ?>', '<?php echo htmlspecialchars($order['user_phone']); ?>')">
                                                    <i class="fas fa-sync me-1"></i>
                                                    重新上传
                                                </button>
                                            <?php else: ?>
                                                <button type="button" class="btn btn-sm btn-primary" 
                                                        onclick="openUploadModal('<?php echo htmlspecialchars($order['order_id']); ?>', '<?php echo htmlspecialchars($order['real_name']); ?>', '<?php echo htmlspecialchars($order['user_phone']); ?>')">
                                                    <i class="fas fa-upload me-1"></i>
                                                    上传报告
                                                </button>
                                            <?php endif; ?>
                                        </td>
                                    </tr>
                                <?php endforeach; ?>
                            </tbody>
                        </table>
                    </div>
                    
                    <!-- 分页 -->
                    <?php if ($total_pages > 1): ?>
                        <div class="d-flex justify-content-center mt-4 mb-3">
                            <nav>
                                <ul class="pagination">
                                    <?php if ($page > 1): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page - 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                <i class="fas fa-chevron-left"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                    
                                    <?php 
                                    $start_page = max(1, $page - 2);
                                    $end_page = min($total_pages, $page + 2);
                                    
                                    for ($i = $start_page; $i <= $end_page; $i++): 
                                    ?>
                                        <li class="page-item <?php echo $i == $page ? 'active' : ''; ?>">
                                            <a class="page-link" href="?page=<?php echo $i; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                <?php echo $i; ?>
                                            </a>
                                        </li>
                                    <?php endfor; ?>
                                    
                                    <?php if ($page < $total_pages): ?>
                                        <li class="page-item">
                                            <a class="page-link" href="?page=<?php echo $page + 1; ?><?php echo $search ? '&search=' . urlencode($search) : ''; ?>">
                                                <i class="fas fa-chevron-right"></i>
                                            </a>
                                        </li>
                                    <?php endif; ?>
                                </ul>
                            </nav>
                        </div>
                    <?php endif; ?>
                <?php endif; ?>
            </div>
        </div>
    </div>

    <!-- 上传报告模态框 -->
    <div class="modal fade" id="uploadModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">
                        <i class="fas fa-upload me-2"></i>
                        上传报告
                    </h5>
                    <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal"></button>
                </div>
                <form method="POST" enctype="multipart/form-data">
                    <div class="modal-body">
                        <input type="hidden" name="action" value="upload_report">
                        <input type="hidden" name="order_id" id="modal_order_id">
                        <input type="hidden" name="real_name" id="modal_real_name">
                        <input type="hidden" name="user_phone" id="modal_user_phone">
                        
                        <div class="mb-3">
                            <label class="form-label">订单信息</label>
                            <div class="card bg-light">
                                <div class="card-body">
                                    <p class="mb-1"><strong>订单号：</strong><span id="display_order_id"></span></p>
                                    <p class="mb-1"><strong>客户姓名：</strong><span id="display_real_name"></span></p>
                                    <p class="mb-0"><strong>手机号：</strong><span id="display_user_phone"></span></p>
                                </div>
                            </div>
                        </div>
                        
                        <div class="mb-3">
                            <label for="pdf_file" class="form-label">
                                <i class="fas fa-file-pdf me-1"></i>
                                选择PDF报告文件
                            </label>
                            <input type="file" class="form-control" id="pdf_file" name="pdf_file" 
                                   accept=".pdf" required>
                            <small class="text-muted">仅支持PDF格式，文件大小不超过50MB</small>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">
                            <i class="fas fa-times me-1"></i>
                            取消
                        </button>
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-upload me-1"></i>
                            上传报告
                        </button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    <script>
        function openUploadModal(orderId, realName, userPhone) {
            document.getElementById('modal_order_id').value = orderId;
            document.getElementById('modal_real_name').value = realName;
            document.getElementById('modal_user_phone').value = userPhone;
            
            document.getElementById('display_order_id').textContent = orderId;
            document.getElementById('display_real_name').textContent = realName;
            document.getElementById('display_user_phone').textContent = userPhone;
            
            new bootstrap.Modal(document.getElementById('uploadModal')).show();
        }
    </script>
</body>
</html>