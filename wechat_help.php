<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信PDF预览帮助</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .content {
            padding: 20px;
        }
        .help-section {
            margin-bottom: 25px;
            padding: 15px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .help-section h3 {
            margin: 0 0 10px 0;
            color: #333;
            font-size: 16px;
        }
        .help-section p {
            margin: 5px 0;
            color: #666;
            font-size: 14px;
            line-height: 1.5;
        }
        .step-list {
            list-style: none;
            padding: 0;
            margin: 10px 0;
        }
        .step-list li {
            padding: 8px 0;
            border-bottom: 1px solid #eee;
            position: relative;
            padding-left: 30px;
        }
        .step-list li:before {
            content: counter(step-counter);
            counter-increment: step-counter;
            position: absolute;
            left: 0;
            top: 8px;
            background: #667eea;
            color: white;
            width: 20px;
            height: 20px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .step-list {
            counter-reset: step-counter;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            text-align: center;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        .device-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 13px;
        }
        .controls {
            text-align: center;
            padding: 20px;
            border-top: 1px solid #eee;
        }
        .icon {
            font-size: 24px;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <div class="icon">📱</div>
            <h1 style="margin: 0; font-size: 20px;">微信PDF预览帮助</h1>
            <p style="margin: 10px 0 0 0; font-size: 14px;">解决PDF预览问题的完整指南</p>
        </div>
        
        <div class="content">
            <div class="device-info" id="deviceInfo">
                <strong>🔍 环境检测</strong><br>
                <span id="userAgent">正在检测您的设备...</span>
            </div>
            
            <div class="help-section">
                <h3>🤖 安卓微信用户</h3>
                <p>安卓微信环境下PDF预览可能不稳定，推荐以下解决方案：</p>
                <ol class="step-list">
                    <li>点击右上角"..."菜单</li>
                    <li>选择"在浏览器中打开"</li>
                    <li>在浏览器中正常预览PDF</li>
                    <li>或者直接下载文件查看</li>
                </ol>
            </div>
            
            <div class="help-section">
                <h3>📱 iOS微信用户</h3>
                <p>iOS微信通常支持PDF预览，如遇问题：</p>
                <ol class="step-list">
                    <li>点击右上角"..."菜单</li>
                    <li>选择"在Safari中打开"</li>
                    <li>在Safari中预览PDF文件</li>
                </ol>
            </div>
            
            <div class="help-section">
                <h3>🔧 预览方案选择</h3>
                <p>我们提供多种预览方案：</p>
                <ul style="list-style: none; padding: 0;">
                    <li style="padding: 5px 0;">📱 <strong>在线预览</strong> - 直接在页面中显示</li>
                    <li style="padding: 5px 0;">🔧 <strong>简化预览</strong> - 轻量级预览模式</li>
                    <li style="padding: 5px 0;">📚 <strong>PDF.js预览</strong> - 专业PDF阅读器</li>
                    <li style="padding: 5px 0;">🔗 <strong>新窗口打开</strong> - 在新页面中打开</li>
                    <li style="padding: 5px 0;">💾 <strong>下载文件</strong> - 保存到本地</li>
                </ul>
            </div>
            
            <div class="help-section">
                <h3>❓ 常见问题</h3>
                <p><strong>Q: 为什么安卓微信无法预览PDF？</strong></p>
                <p>A: 安卓微信的WebView对PDF支持有限，建议使用浏览器打开。</p>
                
                <p><strong>Q: 如何确保最佳预览效果？</strong></p>
                <p>A: 建议使用Chrome、Safari等现代浏览器，或下载文件到本地查看。</p>
                
                <p><strong>Q: 文件下载后在哪里？</strong></p>
                <p>A: 通常在手机的"下载"文件夹中，可用PDF阅读器打开。</p>
            </div>
        </div>
        
        <div class="controls">
            <a href="javascript:history.back()" class="btn">
                ← 返回
            </a>
            <a href="index.php" class="btn btn-success">
                🏠 首页
            </a>
        </div>
    </div>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示用户代理信息
            const userAgent = navigator.userAgent;
            const deviceInfo = document.getElementById('userAgent');
            
            let info = '';
            if (userAgent.indexOf('MicroMessenger') > -1) {
                info += '✅ 微信环境 ';
                
                if (userAgent.indexOf('Android') > -1) {
                    info += '🤖 安卓系统';
                } else if (userAgent.indexOf('iPhone') > -1 || userAgent.indexOf('iPad') > -1) {
                    info += '📱 iOS系统';
                }
                
                // 提取微信版本
                const wechatMatch = userAgent.match(/MicroMessenger\/([0-9\.]+)/);
                if (wechatMatch) {
                    info += ' (微信' + wechatMatch[1] + ')';
                }
            } else {
                info += '🌐 普通浏览器环境';
            }
            
            deviceInfo.textContent = info;
        });
    </script>
</body>
</html>
