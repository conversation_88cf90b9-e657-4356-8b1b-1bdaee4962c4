<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        die('报告不存在');
    }
    
    $pdf_path = $report['pdf_path'];
    
    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }
    
    // 环境检测
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    $is_android = strpos($user_agent, 'Android') !== false;
    
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告预览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: Arial, sans-serif;
            background-color: #f0f0f0;
        }
        .header {
            background: #667eea;
            color: white;
            padding: 10px;
            text-align: center;
            font-size: 14px;
        }
        .content {
            padding: 10px;
            text-align: center;
        }
        .pdf-frame {
            width: 100%;
            height: calc(100vh - 120px);
            border: 1px solid #ddd;
            border-radius: 5px;
            background: white;
        }
        .btn {
            display: inline-block;
            padding: 10px 20px;
            margin: 5px;
            background: #667eea;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            font-size: 14px;
        }
        .btn:hover {
            background: #5a6fd8;
        }
        .btn-download {
            background: #28a745;
        }
        .btn-download:hover {
            background: #218838;
        }
        .loading {
            padding: 20px;
            color: #666;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            margin: 10px;
            border-radius: 5px;
            font-size: 14px;
        }
        .controls {
            position: fixed;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.9);
            padding: 5px;
            border-radius: 15px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.2);
        }
    </style>
</head>
<body>
    <div class="header">
        📄 <?php echo htmlspecialchars($report['name']); ?> - <?php echo htmlspecialchars($report['order_number']); ?>
    </div>
    
    <div class="content">
        <div class="loading" id="loading">
            <div style="font-size: 24px;">📄</div>
            加载中...
        </div>
        
        <iframe id="pdfFrame" 
                src="<?php echo htmlspecialchars($pdf_path); ?>" 
                class="pdf-frame" 
                style="display:none;">
        </iframe>
        
        <div id="errorMessage" class="error" style="display:none;">
            <strong>预览失败</strong><br>
            <?php if ($is_wechat && $is_android): ?>
                安卓微信环境可能不支持PDF预览。<br>
                建议：点击右上角"..."选择"在浏览器中打开"
            <?php else: ?>
                您的浏览器可能不支持PDF预览。
            <?php endif; ?>
            <br><br>
            <a href="download.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn btn-download">下载文件</a>
        </div>
    </div>
    
    <div class="controls">
        <a href="javascript:history.back()" class="btn">返回</a>
        <a href="download.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn btn-download">下载</a>
    </div>
    
    <script>
        function showPDF() {
            document.getElementById("loading").style.display = "none";
            document.getElementById("pdfFrame").style.display = "block";
        }
        
        function showError() {
            document.getElementById("loading").style.display = "none";
            document.getElementById("errorMessage").style.display = "block";
        }
        
        // 简单的加载处理
        setTimeout(function() {
            const iframe = document.getElementById("pdfFrame");
            
            iframe.onload = function() {
                showPDF();
            };
            
            iframe.onerror = function() {
                showError();
            };
            
            // 检测是否加载成功
            setTimeout(function() {
                try {
                    // 尝试访问iframe内容来检测是否加载成功
                    if (iframe.contentDocument || iframe.contentWindow) {
                        showPDF();
                    } else {
                        showError();
                    }
                } catch (e) {
                    // 跨域错误通常意味着PDF加载成功
                    showPDF();
                }
            }, 2000);
            
            // 最终超时
            setTimeout(function() {
                if (iframe.style.display === "none") {
                    showError();
                }
            }, 5000);
        }, 500);
    </script>
</body>
</html>

<?php
} catch (Exception $e) {
    die('查询出错：' . $e->getMessage());
}
?>
