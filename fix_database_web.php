<?php
// 简单的数据库修复工具
$message = '';
$error = '';

if (isset($_GET['action']) && $_GET['action'] === 'fix') {
    try {
        // 数据库连接信息
        $host = 'localhost';
        $dbname = 'mall_healthcare1';
        $username = 'mall_healthcare1';
        $password = 'XYzh6WpXaNc8h9yJ';
        
        $pdo = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        
        // 检查表是否存在
        $stmt = $pdo->query("SHOW TABLES LIKE 'reports'");
        if ($stmt->rowCount() == 0) {
            // 创建表
            $createTable = "
                CREATE TABLE reports (
                    id INT AUTO_INCREMENT PRIMARY KEY,
                    name VARCHAR(100) NOT NULL COMMENT '姓名',
                    phone VARCHAR(20) NOT NULL COMMENT '手机号',
                    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
                    pdf_path VARCHAR(255) NOT NULL COMMENT 'PDF文件路径',
                    upload_time INT(11) NOT NULL COMMENT '上传时间(Unix时间戳)',
                    INDEX idx_name (name),
                    INDEX idx_phone (phone),
                    INDEX idx_order_number (order_number)
                )
            ";
            $pdo->exec($createTable);
            $message .= "✓ 创建 reports 表成功！<br>";
        } else {
            // 检查字段类型
            $stmt = $pdo->query("DESCRIBE reports upload_time");
            $field = $stmt->fetch();
            
            if (strpos($field['Type'], 'timestamp') !== false || strpos($field['Type'], 'datetime') !== false) {
                // 需要修改字段类型
                $message .= "检测到 upload_time 字段类型为: {$field['Type']}<br>";
                
                // 先备份现有数据
                $stmt = $pdo->query("SELECT id, upload_time FROM reports");
                $records = $stmt->fetchAll();
                
                // 修改字段类型
                $pdo->exec("ALTER TABLE reports MODIFY COLUMN upload_time INT(11) NOT NULL COMMENT '上传时间(Unix时间戳)'");
                $message .= "✓ 修改 upload_time 字段类型为 INT 成功！<br>";
                
                // 转换现有数据
                if (!empty($records)) {
                    $converted = 0;
                    foreach ($records as $record) {
                        $upload_time = $record['upload_time'];
                        if (!is_numeric($upload_time)) {
                            $timestamp = strtotime($upload_time);
                            if ($timestamp !== false) {
                                $updateStmt = $pdo->prepare("UPDATE reports SET upload_time = ? WHERE id = ?");
                                $updateStmt->execute([$timestamp, $record['id']]);
                                $converted++;
                            }
                        }
                    }
                    $message .= "✓ 转换了 $converted 条记录的时间格式！<br>";
                }
            } else {
                $message .= "✓ upload_time 字段类型已经是正确的 INT 类型！<br>";
            }
        }
        
        $message .= "<br><strong>✓ 数据库修复完成！现在可以正常使用系统了。</strong>";
        
    } catch (Exception $e) {
        $error = "修复失败: " . $e->getMessage();
    }
}
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>数据库修复工具</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 50px auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .btn {
            background: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 5px;
            text-decoration: none;
            display: inline-block;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
        .success {
            background: #d4edda;
            color: #155724;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .error {
            background: #f8d7da;
            color: #721c24;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
        .info {
            background: #d1ecf1;
            color: #0c5460;
            padding: 15px;
            border-radius: 5px;
            margin: 10px 0;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>数据库修复工具</h1>
        
        <?php if ($message): ?>
            <div class="success"><?php echo $message; ?></div>
            <a href="index.php" class="btn">返回首页</a>
            <a href="admin/login.php" class="btn">管理员登录</a>
        <?php elseif ($error): ?>
            <div class="error"><?php echo htmlspecialchars($error); ?></div>
            <a href="?" class="btn">重试</a>
        <?php else: ?>
            <div class="info">
                <h3>检测到数据库字段类型问题</h3>
                <p>系统检测到 <code>reports</code> 表的 <code>upload_time</code> 字段可能存在类型不匹配的问题。</p>
                <p><strong>修复内容：</strong></p>
                <ul>
                    <li>检查并创建 reports 表（如果不存在）</li>
                    <li>将 upload_time 字段从 TIMESTAMP 类型改为 INT 类型</li>
                    <li>转换现有数据的时间格式为 Unix 时间戳</li>
                    <li>确保系统正常运行</li>
                </ul>
                <p><strong>注意：</strong>此操作会修改数据库表结构，但会保留所有现有数据。</p>
            </div>
            <a href="?action=fix" class="btn">开始修复</a>
            <a href="index.php" class="btn" style="background: #6c757d;">取消</a>
        <?php endif; ?>
    </div>
</body>
</html>