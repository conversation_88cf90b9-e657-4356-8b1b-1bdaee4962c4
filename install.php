<?php
/**
 * 报告查询系统安装脚本
 * 用于检查系统环境和初始化数据库
 */

// 检查PHP版本
if (version_compare(PHP_VERSION, '7.4.0') < 0) {
    die('错误：需要PHP 7.4或更高版本，当前版本：' . PHP_VERSION);
}

// 检查必需的扩展
$required_extensions = ['pdo', 'pdo_mysql', 'fileinfo'];
$missing_extensions = [];

foreach ($required_extensions as $ext) {
    if (!extension_loaded($ext)) {
        $missing_extensions[] = $ext;
    }
}

if (!empty($missing_extensions)) {
    die('错误：缺少必需的PHP扩展：' . implode(', ', $missing_extensions));
}

// 检查目录权限
$upload_dir = 'uploads/reports/';
if (!is_dir($upload_dir)) {
    if (!mkdir($upload_dir, 0755, true)) {
        die('错误：无法创建上传目录：' . $upload_dir);
    }
}

if (!is_writable($upload_dir)) {
    die('错误：上传目录没有写入权限：' . $upload_dir);
}

$status = [];
$status[] = '✓ PHP版本检查通过：' . PHP_VERSION;
$status[] = '✓ 必需扩展检查通过';
$status[] = '✓ 上传目录权限检查通过';

// 数据库连接测试
try {
    // 数据库配置
    $host = 'localhost';
    $dbname = 'mall_healthcare1';
    $username = 'mall_healthcare1';
    $password = 'XYzh6WpXaNc8h9yJ';
    
    $dsn = "mysql:host=$host;dbname=$dbname;charset=utf8mb4";
    $conn = new PDO($dsn, $username, $password, [
        PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
        PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC
    ]);
    
    if ($conn) {
        $status[] = '✓ 数据库连接成功';
        
        // 检查表是否存在
        $tables = ['admins', 'reports'];
        $existing_tables = [];
        
        foreach ($tables as $table) {
            $stmt = $conn->prepare("SHOW TABLES LIKE ?");
            $stmt->execute([$table]);
            if ($stmt->fetch()) {
                $existing_tables[] = $table;
            }
        }
        
        if (count($existing_tables) === count($tables)) {
            $status[] = '✓ 数据库表已存在';
        } else {
            $status[] = '⚠ 需要创建数据库表，请运行 database.sql';
        }
    }
} catch (Exception $e) {
    $status[] = '✗ 数据库连接失败：' . $e->getMessage();
    $status[] = '请检查 config/database.php 中的数据库配置';
}

?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>系统安装检查 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        .install-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .install-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .status-item {
            padding: 0.5rem 0;
            border-bottom: 1px solid #e9ecef;
        }
        .status-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8">
                <div class="install-container">
                    <div class="install-header">
                        <i class="fas fa-cogs fa-3x mb-3"></i>
                        <h2>报告查询系统</h2>
                        <p class="mb-0">系统环境检查</p>
                    </div>
                    <div class="p-4">
                        <h4 class="mb-3">系统状态</h4>
                        <?php foreach ($status as $item): ?>
                            <div class="status-item">
                                <?php if (strpos($item, '✓') === 0): ?>
                                    <i class="fas fa-check-circle text-success me-2"></i>
                                    <span class="text-success"><?php echo htmlspecialchars($item); ?></span>
                                <?php elseif (strpos($item, '⚠') === 0): ?>
                                    <i class="fas fa-exclamation-triangle text-warning me-2"></i>
                                    <span class="text-warning"><?php echo htmlspecialchars($item); ?></span>
                                <?php else: ?>
                                    <i class="fas fa-times-circle text-danger me-2"></i>
                                    <span class="text-danger"><?php echo htmlspecialchars($item); ?></span>
                                <?php endif; ?>
                            </div>
                        <?php endforeach; ?>
                        
                        <div class="mt-4">
                            <h5>安装步骤</h5>
                            <ol>
                                <li>确保MySQL数据库服务正在运行</li>
                                <li>创建数据库和用户（参考README.md）</li>
                                <li>导入database.sql文件到数据库</li>
                                <li>检查config/database.php配置</li>
                                <li>确保uploads目录有写入权限</li>
                            </ol>
                        </div>
                        
                        <div class="mt-4 text-center">
                            <a href="index.php" class="btn btn-primary me-2">
                                <i class="fas fa-home me-1"></i>
                                进入系统首页
                            </a>
                            <a href="admin/login.php" class="btn btn-secondary">
                                <i class="fas fa-user-shield me-1"></i>
                                管理员登录
                            </a>
                        </div>
                        
                        <div class="mt-3 text-center">
                            <small class="text-muted">
                                默认管理员账号：admin / admin123
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>