-- 报告查询系统数据库表结构
-- 数据库名: mall_healthcare1
-- 用户名: mall_healthcare1
-- 密码: XYzh6WpXaNc8h9yJ

USE mall_healthcare1;

-- 管理员表
CREATE TABLE IF NOT EXISTS admins (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(50) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- 插入默认管理员账号 (用户名: admin, 密码: admin123)
INSERT INTO admins (username, password) VALUES 
('admin', '$2y$10$7rLSvRVyTQORapkDOqmkhetjF6H9lJHiBJk0MdGWMhJ8tUCEqyJ2G');

-- 报告表
CREATE TABLE IF NOT EXISTS reports (
    id INT AUTO_INCREMENT PRIMARY KEY,
    name VARCHAR(100) NOT NULL COMMENT '姓名',
    phone VARCHAR(20) NOT NULL COMMENT '手机号',
    order_number VARCHAR(50) NOT NULL UNIQUE COMMENT '订单号',
    pdf_path VARCHAR(255) NOT NULL COMMENT 'PDF文件路径',
    upload_time INT(11) NOT NULL COMMENT '上传时间(Unix时间戳)',
    INDEX idx_name (name),
    INDEX idx_phone (phone),
    INDEX idx_order_number (order_number)
);

-- 创建上传目录（需要在PHP中处理）
-- uploads/reports/ 目录用于存储PDF文件