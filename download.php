<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        // 调试信息
        $debug_stmt = $conn->query("SELECT COUNT(*) as count FROM reports");
        $total_count = $debug_stmt->fetch(PDO::FETCH_ASSOC)['count'];
        die("报告不存在。调试信息：查询ID={$report_id}, 数据库中共有{$total_count}条记录。<br><a href='debug_reports.php?test_id={$report_id}'>点击调试</a>");
    }
    
    $pdf_path = $report['pdf_path'];
    
    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }
    
    // 检测用户代理
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    
    // 生成安全的文件名
    $safe_filename = '报告_' . preg_replace('/[^\w\-_\.]/', '_', $report['name']) . '_' . preg_replace('/[^\w\-_\.]/', '_', $report['order_number']) . '.pdf';
    
    if ($is_wechat) {
        // 在微信环境中，提供特殊的下载处理
        // 由于微信的限制，我们提供一个下载页面而不是直接下载
        echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载报告</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 400px;
            margin: 50px auto;
            background: white;
            border-radius: 10px;
            padding: 30px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .icon {
            font-size: 48px;
            color: #1aad19;
            margin-bottom: 20px;
        }
        .title {
            font-size: 20px;
            font-weight: bold;
            margin-bottom: 10px;
            color: #333;
        }
        .info {
            color: #666;
            margin-bottom: 20px;
            line-height: 1.5;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: #1aad19;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            border: none;
            cursor: pointer;
            font-size: 16px;
        }
        .btn:hover {
            background: #179b16;
        }
        .btn-secondary {
            background: #6c757d;
        }
        .btn-secondary:hover {
            background: #5a6268;
        }
        .steps {
            text-align: left;
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 20px 0;
        }
        .steps ol {
            margin: 0;
            padding-left: 20px;
        }
        .steps li {
            margin: 5px 0;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📄</div>
        <div class="title">报告下载</div>
        <div class="info">
            <strong>姓名：</strong>' . htmlspecialchars($report['name']) . '<br>
            <strong>订单号：</strong>' . htmlspecialchars($report['order_number']) . '<br>
            <strong>文件名：</strong>' . htmlspecialchars($safe_filename) . '
        </div>
        
        <div class="steps">
            <strong>微信中下载步骤：</strong>
            <ol>
                <li>点击下方"直接下载"按钮</li>
                <li>在弹出的页面中，点击右上角"..."</li>
                <li>选择"在浏览器中打开"</li>
                <li>在浏览器中即可正常下载</li>
            </ol>
        </div>
        
        <div>
            <a href="?id=' . htmlspecialchars($report_id) . '&force=1" class="btn">直接下载</a>
            <a href="javascript:history.back()" class="btn btn-secondary">返回</a>
        </div>
        
        <div style="margin-top: 20px; font-size: 12px; color: #999;">
            如遇下载问题，请联系客服获取帮助
        </div>
    </div>
</body>
</html>';
    } else {
        // 在普通浏览器中或强制下载时，直接提供文件下载
        header('Content-Type: application/pdf');
        header('Content-Disposition: attachment; filename="' . $safe_filename . '"');
        header('Content-Length: ' . filesize($pdf_path));
        header('Cache-Control: must-revalidate');
        header('Pragma: public');
        
        // 清除输出缓冲区
        if (ob_get_level()) {
            ob_end_clean();
        }
        
        readfile($pdf_path);
        exit;
    }
    
} catch (Exception $e) {
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>下载错误</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; text-align: center; }
        .error { color: red; margin: 20px 0; }
    </style>
</head>
<body>
    <h2>下载失败</h2>
    <div class="error">错误信息：' . htmlspecialchars($e->getMessage()) . '</div>
    <a href="javascript:history.back()" style="color: #1aad19;">返回上一页</a>
</body>
</html>';
}

// 处理强制下载请求
if (isset($_GET['force']) && $_GET['force'] == '1' && isset($pdf_path) && file_exists($pdf_path)) {
    header('Content-Type: application/pdf');
    header('Content-Disposition: attachment; filename="' . $safe_filename . '"');
    header('Content-Length: ' . filesize($pdf_path));
    header('Cache-Control: must-revalidate');
    header('Pragma: public');
    
    if (ob_get_level()) {
        ob_end_clean();
    }
    
    readfile($pdf_path);
    exit;
}
?>