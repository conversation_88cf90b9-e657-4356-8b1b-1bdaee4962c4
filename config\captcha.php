<?php
/**
 * 验证码生成和验证类
 * 用于防止暴力破解登录
 */
class Captcha {
    
    /**
     * 生成验证码图片
     */
    public static function generate() {
        session_start();
        
        // 生成随机验证码
        $code = '';
        $chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
        for ($i = 0; $i < 4; $i++) {
            $code .= $chars[rand(0, strlen($chars) - 1)];
        }
        
        // 存储到session
        $_SESSION['captcha_code'] = $code;
        $_SESSION['captcha_time'] = time();
        
        // 创建图片
        $width = 120;
        $height = 40;
        $image = imagecreate($width, $height);
        
        // 设置颜色
        $bg_color = imagecolorallocate($image, 240, 240, 240);
        $text_color = imagecolorallocate($image, 50, 50, 50);
        $line_color = imagecolorallocate($image, 200, 200, 200);
        
        // 填充背景
        imagefill($image, 0, 0, $bg_color);
        
        // 添加干扰线
        for ($i = 0; $i < 5; $i++) {
            imageline($image, rand(0, $width), rand(0, $height), rand(0, $width), rand(0, $height), $line_color);
        }
        
        // 添加验证码文字
        $font_size = 16;
        for ($i = 0; $i < strlen($code); $i++) {
            $x = 20 + $i * 20;
            $y = rand(25, 30);
            imagestring($image, $font_size, $x, $y, $code[$i], $text_color);
        }
        
        // 输出图片
        header('Content-Type: image/png');
        header('Cache-Control: no-cache, no-store, must-revalidate');
        header('Pragma: no-cache');
        header('Expires: 0');
        
        imagepng($image);
        imagedestroy($image);
    }
    
    /**
     * 验证验证码
     */
    public static function verify($input_code) {
        session_start();
        
        // 检查验证码是否存在
        if (!isset($_SESSION['captcha_code']) || !isset($_SESSION['captcha_time'])) {
            return false;
        }
        
        // 检查验证码是否过期（5分钟）
        if (time() - $_SESSION['captcha_time'] > 300) {
            unset($_SESSION['captcha_code']);
            unset($_SESSION['captcha_time']);
            return false;
        }
        
        // 验证码比较（不区分大小写）
        $result = strtoupper($input_code) === strtoupper($_SESSION['captcha_code']);
        
        // 验证后清除验证码
        if ($result) {
            unset($_SESSION['captcha_code']);
            unset($_SESSION['captcha_time']);
        }
        
        return $result;
    }
    
    /**
     * 清除验证码
     */
    public static function clear() {
        session_start();
        unset($_SESSION['captcha_code']);
        unset($_SESSION['captcha_time']);
    }
}
?>