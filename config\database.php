<?php
// 数据库配置文件
class Database {
    private $host = 'localhost';
    private $db_name = 'mall_healthcare1';
    private $username = 'mall_healthcare1';
    private $password = 'XYzh6WpXaNc8h9yJ';
    private $charset = 'utf8mb4';
    public $conn;

    // 获取数据库连接
    public function getConnection() {
        $this->conn = null;
        try {
            $dsn = "mysql:host=" . $this->host . ";dbname=" . $this->db_name . ";charset=" . $this->charset;
            $this->conn = new PDO($dsn, $this->username, $this->password);
            $this->conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $this->conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        } catch(PDOException $exception) {
            echo "连接失败: " . $exception->getMessage();
        }
        return $this->conn;
    }
}
?>