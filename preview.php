<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);

    if (!$report) {
        // 调试信息
        $debug_stmt = $conn->query("SELECT COUNT(*) as count FROM reports");
        $total_count = $debug_stmt->fetch(PDO::FETCH_ASSOC)['count'];
        die("报告不存在。调试信息：查询ID={$report_id}, 数据库中共有{$total_count}条记录。<br><a href='debug_reports.php?test_id={$report_id}'>点击调试</a>");
    }

    $pdf_path = $report['pdf_path'];

    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }

    // 构建完整的PDF URL
    $pdf_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $pdf_path;

    // 高级环境检测
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    $is_android = strpos($user_agent, 'Android') !== false;
    $is_ios = strpos($user_agent, 'iPhone') !== false || strpos($user_agent, 'iPad') !== false;

    // 检测微信版本
    $wechat_version = '';
    if ($is_wechat && preg_match('/MicroMessenger\/([0-9\.]+)/', $user_agent, $matches)) {
        $wechat_version = $matches[1];
    }

    // 检测安卓版本
    $android_version = '';
    if ($is_android && preg_match('/Android ([0-9\.]+)/', $user_agent, $matches)) {
        $android_version = $matches[1];
    }
    
    echo '<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告预览</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .content {
            padding: 0;
        }
        .pdf-container {
            width: 100%;
            height: calc(100vh - 120px);
            border: none;
            display: block;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 10px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        .error-message, .warning-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .warning-message {
            background: #fff3cd;
            border-color: #ffeaa7;
            color: #856404;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        .environment-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
            padding: 15px;
            margin: 20px;
            border-radius: 8px;
            font-size: 0.9rem;
        }
        .preview-options {
            padding: 20px;
            text-align: center;
        }
        .preview-options .btn {
            display: block;
            width: 80%;
            margin: 10px auto;
            max-width: 300px;
        }
        @media (max-width: 768px) {
            .header {
                padding: 10px;
            }
            .header h2 {
                font-size: 1.2rem;
                margin: 0;
            }
            .header p {
                font-size: 0.9rem;
                margin: 5px 0 0 0;
            }
            .pdf-container {
                height: calc(100vh - 100px);
            }
            .controls {
                bottom: 10px;
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>📄 报告预览</h2>
            <p>姓名：' . htmlspecialchars($report['name']) . ' | 订单号：' . htmlspecialchars($report['order_number']) . '</p>
        </div>';

        // 根据环境显示不同的内容
        if ($is_wechat && $is_android) {
            // 安卓微信环境 - 提供多种选择
            echo '
        <div class="content" style="text-align: center; padding: 40px 20px;">
            <div style="font-size: 48px; margin-bottom: 20px;">🤖</div>
            <h4 style="color: #dc3545; margin-bottom: 15px;">检测到安卓微信环境</h4>
            <p style="margin: 15px 0; color: #666;">由于安卓微信的技术限制，PDF预览和下载功能不可用。</p>
            <p style="margin: 15px 0; color: #666;">我们为您准备了专门的解决方案！</p>

            <div style="background: #fff3cd; border: 1px solid #ffeaa7; color: #856404; padding: 15px; border-radius: 8px; margin: 20px 0; text-align: left;">
                <strong>环境信息：</strong><br>
                微信版本：' . htmlspecialchars($wechat_version) . '<br>
                安卓版本：' . htmlspecialchars($android_version) . '
            </div>

            <div style="margin: 30px 0;">
                <a href="android_wechat_solution.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-success" style="font-size: 18px; padding: 15px 30px; margin: 10px;">
                    � 安卓微信专用解决方案
                </a>
            </div>

            <div style="margin: 20px 0;">
                <a href="force_download.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-warning" style="margin: 5px;">
                    � 强制下载
                </a>
                <a href="wechat_help.php" class="btn" style="background: #6c757d; margin: 5px;">
                    ❓ 查看帮助
                </a>
            </div>

            <script>
                // 3秒后提示跳转
                setTimeout(function() {
                    if (confirm("建议使用专用解决方案来查看PDF文件，是否现在跳转？")) {
                        window.location.href = "android_wechat_solution.php?id=' . htmlspecialchars($report_id) . '";
                    }
                }, 3000);
            </script>
        </div>

        <div id="iframeContainer" style="display:none;">
            <div class="loading" id="loading">
                <div style="font-size: 2rem;">📄</div>
                正在加载PDF文件...
            </div>

            <iframe id="pdfFrame" src="' . htmlspecialchars($pdf_path) . '" class="pdf-container" style="display:none;">
            </iframe>

            <div id="errorMessage" class="error-message" style="display:none;">
                <h4>❌ 预览失败</h4>
                <p>安卓微信环境下PDF预览可能不稳定。</p>
                <p><strong>建议操作：</strong></p>
                <ol style="text-align: left; display: inline-block;">
                    <li>点击右上角"..."菜单</li>
                    <li>选择"在浏览器中打开"</li>
                    <li>或者直接下载文件查看</li>
                </ol>
                <div style="margin: 15px 0;">
                    <a href="javascript:void(0)" onclick="retryPreview()" class="btn" style="background: #17a2b8;">🔄 重试</a>
                    <a href="preview_simple.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-warning">简化预览</a>
                    <a href="download.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-success">立即下载</a>
                    <a href="wechat_help.php" class="btn" style="background: #6c757d;">❓ 帮助</a>
                </div>
            </div>
        </div>';
        } else if ($is_wechat && $is_ios) {
            // iOS微信环境 - 通常支持较好
            echo '
        <div class="environment-info">
            <strong>📱 检测到iOS微信环境</strong><br>
            微信版本：' . htmlspecialchars($wechat_version) . '
        </div>

        <div class="content">
            <div class="loading" id="loading">
                <div style="font-size: 2rem;">📄</div>
                正在加载PDF文件...
            </div>

            <iframe id="pdfFrame" src="' . htmlspecialchars($pdf_path) . '" class="pdf-container" style="display:none;">
            </iframe>

            <div id="errorMessage" class="warning-message" style="display:none;">
                <h4>⚠️ 预览提示</h4>
                <p>如果预览有问题，请尝试：</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>点击右上角"..."选择"在Safari中打开"</li>
                    <li>使用PDF.js预览器</li>
                    <li>或者下载文件到本地查看</li>
                </ul>
                <a href="preview_pdfjs.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-warning">PDF.js预览</a>
                <a href="download.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-success">下载文件</a>
            </div>
        </div>';
        } else {
            // 普通浏览器环境
            echo '
        <div class="content">
            <div class="loading" id="loading">
                <div style="font-size: 2rem;">📄</div>
                正在加载PDF文件...
            </div>

            <iframe id="pdfFrame" src="' . htmlspecialchars($pdf_path) . '" class="pdf-container" style="display:none;">
            </iframe>

            <div id="errorMessage" class="error-message" style="display:none;">
                <h4>📱 PDF预览提示</h4>
                <p>您的浏览器可能不支持直接预览PDF文件。</p>
                <p>建议使用以下方式查看：</p>
                <ul style="text-align: left; display: inline-block;">
                    <li>使用PDF.js在线预览器</li>
                    <li>在Chrome、Safari等现代浏览器中打开</li>
                    <li>更新您的浏览器到最新版本</li>
                    <li>下载文件到本地查看</li>
                </ul>
                <a href="preview_pdfjs.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-warning">PDF.js预览</a>
                <a href="download.php?id=' . htmlspecialchars($report_id) . '" class="btn btn-success">下载文件</a>
            </div>
        </div>';
        }

        echo '
        <div class="controls">
            <a href="javascript:history.back()" class="btn">
                ← 返回
            </a>
        </div>
    </div>

    <script>
        // 环境检测
        const isWeChatAndroid = navigator.userAgent.indexOf("MicroMessenger") > -1 &&
                               navigator.userAgent.indexOf("Android") > -1;
        const isWeChatIOS = navigator.userAgent.indexOf("MicroMessenger") > -1 &&
                           (navigator.userAgent.indexOf("iPhone") > -1 || navigator.userAgent.indexOf("iPad") > -1);

        function showPDF() {
            const loading = document.getElementById("loading");
            const pdfFrame = document.getElementById("pdfFrame");

            if (loading) loading.style.display = "none";
            if (pdfFrame) pdfFrame.style.display = "block";
        }

        function showError() {
            const loading = document.getElementById("loading");
            const errorMessage = document.getElementById("errorMessage");

            if (loading) loading.style.display = "none";
            if (errorMessage) errorMessage.style.display = "block";
        }

        function tryIframePreview() {
            const iframeContainer = document.getElementById("iframeContainer");
            const previewOptions = document.querySelector(".preview-options");

            if (iframeContainer) {
                iframeContainer.style.display = "block";
                if (previewOptions) previewOptions.style.display = "none";

                // 重置状态
                const loading = document.getElementById("loading");
                const errorMessage = document.getElementById("errorMessage");
                const iframe = document.getElementById("pdfFrame");

                if (loading) loading.style.display = "block";
                if (errorMessage) errorMessage.style.display = "none";
                if (iframe) iframe.style.display = "none";

                // 设置iframe加载超时
                let loadTimeout;

                if (iframe) {
                    // 监听iframe加载
                    iframe.onload = function() {
                        clearTimeout(loadTimeout);
                        showPDF();
                    };

                    iframe.onerror = function() {
                        clearTimeout(loadTimeout);
                        showError();
                    };

                    // 设置超时处理
                    loadTimeout = setTimeout(function() {
                        showError();
                    }, 8000); // 8秒超时
                }
            }
        }

        function retryPreview() {
            const iframe = document.getElementById("pdfFrame");
            if (iframe) {
                // 重新加载iframe
                iframe.src = iframe.src;

                // 重置显示状态
                const loading = document.getElementById("loading");
                const errorMessage = document.getElementById("errorMessage");

                if (loading) loading.style.display = "block";
                if (errorMessage) errorMessage.style.display = "none";
                iframe.style.display = "none";

                // 重新设置超时
                setTimeout(function() {
                    if (iframe.style.display === "none") {
                        showError();
                    }
                }, 6000);
            }
        }

        // 根据环境自动处理
        document.addEventListener("DOMContentLoaded", function() {
            if (isWeChatAndroid) {
                // 安卓微信环境 - 不自动加载，让用户选择
                console.log("安卓微信环境，等待用户选择预览方式");
            } else if (isWeChatIOS) {
                // iOS微信环境 - 延迟加载
                setTimeout(function() {
                    showPDF();
                }, 1000);

                // 设置错误检测
                setTimeout(function() {
                    const iframe = document.getElementById("pdfFrame");
                    if (iframe && iframe.style.display === "none") {
                        showError();
                    }
                }, 5000);
            } else {
                // 普通浏览器 - 正常加载
                setTimeout(function() {
                    showPDF();
                }, 800);

                // 设置错误检测
                setTimeout(function() {
                    const iframe = document.getElementById("pdfFrame");
                    if (iframe && iframe.style.display === "none") {
                        showError();
                    }
                }, 4000);
            }
        });

        // 添加页面可见性检测
        document.addEventListener("visibilitychange", function() {
            if (document.visibilityState === "visible") {
                // 页面重新可见时，检查iframe状态
                const iframe = document.getElementById("pdfFrame");
                if (iframe && iframe.style.display === "block") {
                    // 尝试刷新iframe
                    setTimeout(function() {
                        iframe.src = iframe.src;
                    }, 500);
                }
            }
        });
    </script>
</body>
</html>';
    
} catch (Exception $e) {
    die('查询出错：' . $e->getMessage());
}
?>