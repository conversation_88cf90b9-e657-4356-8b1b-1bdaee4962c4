<?php
// 微信小程序兼容性测试页面
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>微信兼容性测试</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .container {
            padding: 20px;
        }
        .test-card {
            background: white;
            border-radius: 15px;
            padding: 30px;
            margin: 20px 0;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.warning {
            background: #fff3cd;
            color: #856404;
            border: 1px solid #ffeaa7;
        }
        .status.info {
            background: #d1ecf1;
            color: #0c5460;
            border: 1px solid #bee5eb;
        }
        .btn {
            margin: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="test-card">
            <h2 class="text-center mb-4">🔧 微信小程序兼容性测试</h2>
            
            <div class="row">
                <div class="col-md-6">
                    <h4>环境检测</h4>
                    <div id="environment-status">检测中...</div>
                    
                    <h4 class="mt-4">用户代理信息</h4>
                    <div class="status info">
                        <small id="user-agent">加载中...</small>
                    </div>
                    
                    <h4 class="mt-4">功能测试</h4>
                    <div>
                        <button class="btn btn-primary btn-preview" data-test="preview">测试预览功能</button>
                        <button class="btn btn-success btn-download" data-test="download">测试下载功能</button>
                    </div>
                    
                    <div id="test-results" class="mt-3"></div>
                </div>
                
                <div class="col-md-6">
                    <h4>解决方案说明</h4>
                    <div class="status info">
                        <strong>已实施的修复：</strong><br>
                        ✅ 创建专门的预览处理页面<br>
                        ✅ 创建专门的下载处理页面<br>
                        ✅ 微信环境自动检测<br>
                        ✅ 用户友好的操作指引<br>
                        ✅ 兼容性脚本自动加载
                    </div>
                    
                    <h4 class="mt-4">使用建议</h4>
                    <div class="status warning">
                        <strong>在微信小程序中：</strong><br>
                        • 预览：会显示兼容性提示<br>
                        • 下载：提供详细操作指引<br>
                        • 如遇问题：点击右上角"在浏览器中打开"
                    </div>
                    
                    <div class="mt-4">
                        <a href="index.php" class="btn btn-primary">返回主页</a>
                        <a href="javascript:location.reload()" class="btn btn-secondary">重新测试</a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 显示用户代理
            document.getElementById('user-agent').textContent = navigator.userAgent;
            
            // 环境检测
            const envStatus = document.getElementById('environment-status');
            let statusHtml = '';
            
            if (window.WeChatHelper) {
                if (WeChatHelper.isWeChatEnvironment()) {
                    statusHtml += '<div class="status success">✅ 检测到微信环境</div>';
                    
                    if (WeChatHelper.isMiniProgramWebView()) {
                        statusHtml += '<div class="status warning">⚠️ 微信小程序WebView环境</div>';
                    } else {
                        statusHtml += '<div class="status info">ℹ️ 微信浏览器环境</div>';
                    }
                } else {
                    statusHtml += '<div class="status info">ℹ️ 普通浏览器环境</div>';
                }
                
                statusHtml += '<div class="status success">✅ 兼容性脚本已加载</div>';
            } else {
                statusHtml += '<div class="status warning">⚠️ 兼容性脚本未加载</div>';
            }
            
            envStatus.innerHTML = statusHtml;
            
            // 测试按钮事件
            document.addEventListener('click', function(e) {
                const testType = e.target.getAttribute('data-test');
                if (testType) {
                    const results = document.getElementById('test-results');
                    const timestamp = new Date().toLocaleTimeString();
                    
                    if (testType === 'preview') {
                        results.innerHTML += `<div class="status info">[${timestamp}] 预览功能测试 - 已触发兼容性处理</div>`;
                    } else if (testType === 'download') {
                        results.innerHTML += `<div class="status info">[${timestamp}] 下载功能测试 - 已触发兼容性处理</div>`;
                    }
                }
            });
        });
    </script>
</body>
</html>