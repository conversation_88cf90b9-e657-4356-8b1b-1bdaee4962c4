<?php
/**
 * 更新管理员密码脚本
 * 用于修复admin123密码哈希值问题
 */

require_once 'config/database.php';

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    if (!$conn) {
        throw new Exception('数据库连接失败');
    }
    
    // 生成正确的admin123密码哈希值
    $correct_password = 'admin123';
    $correct_hash = '$2y$10$7rLSvRVyTQORapkDOqmkhetjF6H9lJHiBJk0MdGWMhJ8tUCEqyJ2G';
    
    // 更新管理员密码
    $stmt = $conn->prepare("UPDATE admins SET password = ? WHERE username = 'admin'");
    $result = $stmt->execute([$correct_hash]);
    
    if ($result) {
        echo "✓ 管理员密码已成功更新！\n";
        echo "用户名: admin\n";
        echo "密码: admin123\n";
        echo "\n现在您可以使用 admin/admin123 登录系统了。\n";
    } else {
        echo "✗ 密码更新失败\n";
    }
    
} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
    echo "\n请确保：\n";
    echo "1. 数据库服务正在运行\n";
    echo "2. 数据库配置正确\n";
    echo "3. admins表已创建\n";
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>密码更新 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            padding: 2rem 0;
        }
        .update-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .update-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="update-container">
                    <div class="update-header">
                        <i class="fas fa-key fa-3x mb-3"></i>
                        <h2>密码更新完成</h2>
                        <p class="mb-0">管理员密码已修复</p>
                    </div>
                    <div class="p-4 text-center">
                        <div class="alert alert-success" role="alert">
                            <i class="fas fa-check-circle me-2"></i>
                            密码更新成功！
                        </div>
                        
                        <div class="mb-4">
                            <h5>登录信息</h5>
                            <p><strong>用户名:</strong> admin</p>
                            <p><strong>密码:</strong> admin123</p>
                        </div>
                        
                        <div class="d-grid gap-2">
                            <a href="admin/login.php" class="btn btn-primary">
                                <i class="fas fa-sign-in-alt me-2"></i>
                                前往管理员登录
                            </a>
                            <a href="index.php" class="btn btn-outline-primary">
                                <i class="fas fa-home me-2"></i>
                                返回首页
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</body>
</html>