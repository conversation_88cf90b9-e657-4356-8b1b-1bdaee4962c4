# 报告查询系统

一个基于PHP和MySQL的报告查询系统，支持管理员后台管理和用户前台查询，适配PC端和手机端。

## 功能特点

### 管理员功能
- 安全登录系统（用户名/密码验证，验证码防暴力破解）
- 订单管理（查看eb_store_order表中的订单）
- 订单搜索和分页功能
- 报告文件上传（从订单直接上传PDF格式报告）
- 报告信息管理（自动获取订单中的姓名、手机号、订单号）
- 报告删除功能
- 密码修改功能
- 上传历史查看
- 响应式后台界面

### 用户功能
- 多种查询方式（姓名、手机号、订单号）
- 报告预览和下载
- 响应式前台界面
- 移动端友好设计
- **🆕 微信小程序WebView兼容性支持**

## 微信小程序兼容性修复

### 问题描述
原系统在微信小程序的WebView中无法正常预览和下载PDF文件。

### 解决方案
1. **专门的预览处理** (`preview.php`)
   - 自动检测微信环境
   - 提供兼容性友好的预览界面
   - 支持iframe嵌入和外部链接

2. **专门的下载处理** (`download.php`)
   - 微信环境下提供操作指引
   - 支持强制下载模式
   - 用户友好的下载流程

3. **兼容性脚本** (`wechat_helper.js`)
   - 自动环境检测
   - 智能交互优化
   - 用户操作指引

4. **服务器配置优化** (`.htaccess`)
   - 微信环境特殊头部设置
   - 允许iframe嵌入
   - 缓存策略优化

### 使用方法
在微信小程序中：
1. **预览报告**：点击预览按钮，系统会自动适配微信环境
2. **下载报告**：点击下载按钮，按照提示操作：
   - 点击"前往下载"
   - 点击右上角"···"
   - 选择"在浏览器中打开"
   - 在浏览器中完成下载

### 测试页面
访问 `wechat_test.php` 可以测试微信兼容性功能。

## 系统要求

- PHP 7.4 或更高版本
- MySQL 5.7 或更高版本
- Web服务器（Apache/Nginx）
- PDO MySQL扩展

## 安装步骤

### 1. 数据库配置

1. 使用现有的MySQL数据库：
   - 数据库名：`mall_healthcare1`
   - 用户名：`mall_healthcare1`
   - 密码：`XYzh6WpXaNc8h9yJ`

2. 导入数据库结构：
```bash
mysql -u mall_healthcare1 -p mall_healthcare1 < database.sql
```

### 2. 文件权限设置

确保上传目录有写入权限：
```bash
chmod 755 uploads/
chmod 755 uploads/reports/
```

### 3. Web服务器配置

将项目文件放置在Web服务器根目录下，确保可以通过浏览器访问。

## 默认账号

### 管理员账号
- 用户名：`admin`
- 密码：`admin123`

## 目录结构

```
report/
├── config/
│   └── database.php          # 数据库配置
├── admin/
│   ├── login.php            # 管理员登录
│   ├── dashboard.php        # 管理员后台
│   └── logout.php           # 退出登录
├── uploads/
│   └── reports/             # PDF文件存储目录
├── index.php                # 用户查询页面
├── preview.php              # 🆕 预览处理页面
├── download.php             # 🆕 下载处理页面
├── wechat_helper.js         # 🆕 微信兼容性脚本
├── wechat_test.php          # 🆕 兼容性测试页面
├── .htaccess                # 🆕 服务器配置
├── database.sql             # 数据库结构
└── README.md               # 说明文档
```

## 使用说明

### 管理员使用

1. 访问 `admin/login.php` 进入管理员登录页面
2. 使用默认账号登录
3. 在后台可以：
   - 添加新报告（填写用户信息并上传PDF文件）
   - 查看所有报告列表
   - 预览和删除报告

### 用户使用

1. 访问 `index.php` 进入查询页面
2. 选择查询方式（姓名/手机号/订单号）
3. 输入相应信息进行查询
4. 查看查询结果，可以预览或下载PDF报告

## 安全特性

- 管理员密码使用bcrypt加密
- SQL注入防护（使用预处理语句）
- 文件上传类型验证
- 会话管理和权限控制
- XSS防护（输出转义）

## 技术栈

- **后端**：PHP 7.4+
- **数据库**：MySQL 5.7+
- **前端**：Bootstrap 5.1.3
- **图标**：Font Awesome 6.0
- **样式**：自定义CSS + 渐变设计

## 浏览器支持

- Chrome 60+
- Firefox 60+
- Safari 12+
- Edge 79+
- 移动端浏览器

## 注意事项

1. 确保 `uploads/reports/` 目录有写入权限
2. 建议在生产环境中修改默认管理员密码
3. 定期备份数据库和上传的文件
4. 建议配置HTTPS以确保数据传输安全

## 故障排除

### 常见问题

1. **文件上传失败**
   - 检查目录权限
   - 检查PHP上传限制设置

2. **数据库连接失败**
   - 检查数据库配置信息
   - 确认数据库服务正在运行

3. **页面显示异常**
   - 检查PHP错误日志
   - 确认所有文件完整上传

## 更新日志

### v2.0.0 (最新)
- ✅ 修复微信小程序WebView兼容性问题
- ✅ 添加智能环境检测
- ✅ 优化用户交互体验
- ✅ 添加兼容性测试页面
- ✅ 新增专门的预览和下载处理页面
- ✅ 优化服务器配置

### v1.0.0
- 初始版本发布
- 基础的报告管理和查询功能
- 响应式设计支持
- 安全的用户认证系统