#	名字	类型	排序规则	属性	空	默认	注释	额外	操作
	1	id 主键	int(11)		UNSIGNED	否	无	订单ID	AUTO_INCREMENT	修改 修改	删除 删除	
更多 更多
	2	pid	int(10)			否	0	父类订单id		修改 修改	删除 删除	
更多 更多
	3	order_id 索引	varchar(32)	utf8_general_ci		否	0	订单号		修改 修改	删除 删除	
更多 更多
	4	trade_no	varchar(100)	utf8_general_ci		否		支付宝订单号		修改 修改	删除 删除	
更多 更多
	5	uid 索引	int(11)		UNSIGNED	否	0	用户id		修改 修改	删除 删除	
更多 更多
	6	real_name	varchar(32)	utf8_general_ci		否		用户姓名		修改 修改	删除 删除	
更多 更多
	7	user_phone	varchar(18)	utf8_general_ci		否		用户电话		修改 修改	删除 删除	
更多 更多
	8	user_address	varchar(100)	utf8_general_ci		否		详细地址		修改 修改	删除 删除	
更多 更多
	9	cart_id	text	utf8_general_ci		是	NULL	购物车id		修改 修改	删除 删除	
更多 更多
	10	freight_price	decimal(12,2)			否	0.00	运费金额		修改 修改	删除 删除	
更多 更多
	11	total_num	int(11)		UNSIGNED	否	0	订单商品总数		修改 修改	删除 删除	
更多 更多
	12	total_price	decimal(12,2)		UNSIGNED	否	0.00	订单总价		修改 修改	删除 删除	
更多 更多
	13	total_postage	decimal(12,2)		UNSIGNED	否	0.00	邮费		修改 修改	删除 删除	
更多 更多
	14	pay_price 索引	decimal(12,2)		UNSIGNED	否	0.00	实际支付金额		修改 修改	删除 删除	
更多 更多
	15	pay_postage	decimal(12,2)		UNSIGNED	否	0.00	支付邮费		修改 修改	删除 删除	
更多 更多
	16	deduction_price	decimal(12,2)		UNSIGNED	否	0.00	抵扣金额		修改 修改	删除 删除	
更多 更多
	17	coupon_id 索引	int(11)		UNSIGNED	否	0	优惠券id		修改 修改	删除 删除	
更多 更多
	18	coupon_price	decimal(12,2)		UNSIGNED	否	0.00	优惠券金额		修改 修改	删除 删除	
更多 更多
	19	paid 索引	tinyint(1)		UNSIGNED	否	0	支付状态		修改 修改	删除 删除	
更多 更多
	20	pay_time 索引	int(11)		UNSIGNED	否	0	支付时间		修改 修改	删除 删除	
更多 更多
	21	pay_type 索引	varchar(32)	utf8_general_ci		否		支付方式		修改 修改	删除 删除	
更多 更多
	22	add_time 索引	int(11)		UNSIGNED	否	0	创建时间		修改 修改	删除 删除	
更多 更多
	23	status 索引	tinyint(1)			否	0	订单状态（-1 : 申请退款 -2 : 退货成功 0：待发货；1：待收货；2：已收货；3：待评价；-1：已退款）		修改 修改	删除 删除	
更多 更多
	24	is_stock_up	tinyint(1)			否	0	是否备货中		修改 修改	删除 删除	
更多 更多
	25	refund_status	tinyint(1)		UNSIGNED	否	0	0 未退款 1 申请中 2 已退款		修改 修改	删除 删除	
更多 更多
	26	refund_type	tinyint(1)			否	0	退款申请类型		修改 修改	删除 删除	
更多 更多
	27	refund_express	varchar(255)	utf8_general_ci		否		退货快递单号		修改 修改	删除 删除	
更多 更多
	28	refund_express_name	varchar(255)	utf8_general_ci		否		退货快递名称		修改 修改	删除 删除	
更多 更多
	29	refund_reason_wap_img	varchar(2000)	utf8_general_ci		否		退款图片		修改 修改	删除 删除	
更多 更多
	30	refund_reason_wap_explain	varchar(255)	utf8_general_ci		否		退款用户说明		修改 修改	删除 删除	
更多 更多
	31	refund_reason_time	int(11)		UNSIGNED	否	0	退款时间		修改 修改	删除 删除	
更多 更多
	32	refund_reason_wap	varchar(255)	utf8_general_ci		否		前台退款原因		修改 修改	删除 删除	
更多 更多
	33	refund_reason	varchar(255)	utf8_general_ci		否		不退款的理由		修改 修改	删除 删除	
更多 更多
	34	refund_price	decimal(12,2)		UNSIGNED	否	0.00	退款金额		修改 修改	删除 删除	
更多 更多
	35	delivery_name	varchar(64)	utf8_general_ci		否		快递名称/送货人姓名		修改 修改	删除 删除	
更多 更多
	36	delivery_code	varchar(50)	utf8_general_ci		否		快递公司编码		修改 修改	删除 删除	
更多 更多
	37	delivery_type	varchar(32)	utf8_general_ci		否		发货类型		修改 修改	删除 删除	
更多 更多
	38	delivery_id	varchar(64)	utf8_general_ci		否		快递单号/手机号		修改 修改	删除 删除	
更多 更多
	39	kuaidi_label	varchar(255)	utf8_general_ci		否		快递单号图片		修改 修改	删除 删除	
更多 更多
	40	kuaidi_task_id	varchar(64)	utf8_general_ci		否		快递单任务id		修改 修改	删除 删除	
更多 更多
	41	kuaidi_order_id	varchar(64)	utf8_general_ci		否		快递单订单号		修改 修改	删除 删除	
更多 更多
	42	fictitious_content	varchar(500)	utf8_general_ci		否		虚拟发货内容		修改 修改	删除 删除	
更多 更多
	43	delivery_uid	int(11)			否	0	配送员id		修改 修改	删除 删除	
更多 更多
	44	gain_integral	decimal(12,2)		UNSIGNED	否	0.00	消费赚取积分		修改 修改	删除 删除	
更多 更多
	45	use_integral	decimal(12,2)		UNSIGNED	否	0.00	使用积分		修改 修改	删除 删除	
更多 更多
	46	back_integral	decimal(12,2)		UNSIGNED	否	0.00	给用户退了多少积分		修改 修改	删除 删除	
更多 更多
	47	spread_uid	int(10)			否	0	推广人uid		修改 修改	删除 删除	
更多 更多
	48	spread_two_uid	int(10)			否	0	上上级推广人uid		修改 修改	删除 删除	
更多 更多
	49	one_brokerage	decimal(12,2)			否	0.00	一级返佣金额		修改 修改	删除 删除	
更多 更多
	50	two_brokerage	decimal(12,2)			否	0.00	二级返佣金额		修改 修改	删除 删除	
更多 更多
	51	mark	varchar(512)	utf8_general_ci		否		备注		修改 修改	删除 删除	
更多 更多
	52	is_del 索引	tinyint(1)		UNSIGNED	否	0	是否删除		修改 修改	删除 删除	
更多 更多
	53	is_cancel	tinyint(1)		UNSIGNED	否	0	是否取消		修改 修改	删除 删除	
更多 更多
	54	unique 索引	char(32)	utf8_general_ci		否		唯一id(md5加密)类似id		修改 修改	删除 删除	
更多 更多
	55	remark	varchar(512)	utf8_general_ci		否		管理员备注		修改 修改	删除 删除	
更多 更多
	56	mer_id	int(10)		UNSIGNED	否	0	商户ID		修改 修改	删除 删除	
更多 更多
	57	is_mer_check	tinyint(3)		UNSIGNED	否	0	商户上传		修改 修改	删除 删除	
更多 更多
	58	combination_id	int(11)		UNSIGNED	否	0	拼团商品id0一般商品		修改 修改	删除 删除	
更多 更多
	59	pink_id	int(11)		UNSIGNED	否	0	拼团id 0没有拼团		修改 修改	删除 删除	
更多 更多
	60	cost	decimal(12,2)		UNSIGNED	否	0.00	成本价		修改 修改	删除 删除	
更多 更多
	61	seckill_id	int(11)		UNSIGNED	否	0	秒杀商品ID		修改 修改	删除 删除	
更多 更多
	62	bargain_id	int(11)		UNSIGNED	否	0	砍价id		修改 修改	删除 删除	
更多 更多
	63	advance_id	int(10)			否	0	预售商品id		修改 修改	删除 删除	
更多 更多
	64	verify_code	varchar(12)	utf8_general_ci		否		核销码		修改 修改	删除 删除	
更多 更多
	65	store_id	int(11)			否	0	门店id		修改 修改	删除 删除	
更多 更多
	66	shipping_type	tinyint(1)			否	1	配送方式 1=快递 ，2=门店自提		修改 修改	删除 删除	
更多 更多
	67	clerk_id	int(11)			否	0	店员id		修改 修改	删除 删除	
更多 更多
	68	is_channel	tinyint(1)		UNSIGNED	否	0	支付渠道(0微信公众号1微信小程序)		修改 修改	删除 删除	
更多 更多
	69	is_remind	tinyint(1)		UNSIGNED	否	0	消息提醒		修改 修改	删除 删除	
更多 更多
	70	is_system_del	tinyint(1)			否	0	后台是否删除		修改 修改	删除 删除	
更多 更多
	71	channel_type	varchar(255)	utf8_general_ci		否		用户访问端标识		修改 修改	删除 删除	
更多 更多
	72	province	varchar(255)	utf8_general_ci		否		用户省份		修改 修改	删除 删除	
更多 更多
	73	express_dump	varchar(502)	utf8_general_ci		否		订单面单打印信息		修改 修改	删除 删除	
更多 更多
	74	virtual_type	tinyint(1)			否	0	虚拟商品类型		修改 修改	删除 删除	
更多 更多
	75	virtual_info	varchar(255)	utf8_general_ci		否		虚拟商品信息		修改 修改	删除 删除	
更多 更多
	76	pay_uid	int(11)			否	0	支付用户uid		修改 修改	删除 删除	
更多 更多
	77	custom_form	text	utf8_general_ci		是	NULL	自定义表单		修改 修改	删除 删除	
更多 更多
	78	staff_id	int(11)			否	0	员工id		修改 修改	删除 删除	
更多 更多
	79	agent_id	int(11)			否	0	代理id		修改 修改	删除 删除	
更多 更多
	80	division_id	int(11)			否	0	事业部id		修改 修改	删除 删除	
更多 更多
	81	staff_brokerage	decimal(12,2)			否	0.00	员工返佣		修改 修改	删除 删除	
更多 更多
	82	agent_brokerage	decimal(12,2)			否	0.00	代理返佣		修改 修改	删除 删除	
更多 更多
	83	division_brokerage	decimal(12,2)			否	0.00	事业部返佣		修改 修改	删除 删除	
更多 更多
	84	is_gift	int(1)			否	0	是否礼品订单		修改 修改	删除 删除	
更多 更多
	85	gift_price	decimal(12,2)			否	0.00	礼品附加费		修改 修改	删除 删除	
更多 更多
	86	gift_uid	int(11)			否	0	接受礼品用户uid		修改 修改	删除 删除	
更多 更多
	87	gift_mark	varchar(255)	utf8_general_ci		否		礼物留言		修改 修改	删除 删除	
