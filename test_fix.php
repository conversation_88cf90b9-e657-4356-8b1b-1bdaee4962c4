<?php
require_once 'config/database.php';

// 测试修复页面
echo "<h1>系统修复测试</h1>";
echo "<p>正在测试数据库连接和查询功能...</p>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>✅ 数据库连接成功</h2>";
    
    // 检查reports表
    $stmt = $conn->query("SHOW TABLES LIKE 'reports'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ reports表存在</p>";
        
        // 检查数据
        $stmt = $conn->query("SELECT COUNT(*) as count FROM reports");
        $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
        echo "<p>📊 数据库中共有 {$count} 条报告记录</p>";
        
        if ($count > 0) {
            // 测试查询功能
            $stmt = $conn->query("SELECT * FROM reports LIMIT 1");
            $test_report = $stmt->fetch(PDO::FETCH_ASSOC);
            
            if ($test_report) {
                echo "<h3>✅ 数据查询测试成功</h3>";
                echo "<p>测试报告ID: {$test_report['id']}</p>";
                echo "<p>姓名: {$test_report['name']}</p>";
                echo "<p>PDF路径: {$test_report['pdf_path']}</p>";
                
                // 测试预览和下载链接
                $test_id = $test_report['id'];
                echo "<h3>功能测试链接：</h3>";
                echo "<p><a href='preview.php?id={$test_id}' target='_blank'>🔍 测试预览功能</a></p>";
                echo "<p><a href='download.php?id={$test_id}' target='_blank'>📥 测试下载功能</a></p>";
                
                // 检查文件是否存在
                $file_exists = file_exists($test_report['pdf_path']) ? '✅ 存在' : '❌ 不存在';
                echo "<p>文件状态: {$file_exists}</p>";
            }
        } else {
            echo "<p>⚠️ 数据库中没有报告数据</p>";
            echo "<p>请先通过管理后台添加一些报告数据</p>";
            echo "<p><a href='admin/dashboard.php'>👉 前往管理后台</a></p>";
        }
    } else {
        echo "<p>❌ reports表不存在，请运行数据库初始化</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<h3>问题排查工具：</h3>";
echo "<p><a href='debug_reports.php'>🔧 详细调试信息</a></p>";
echo "<p><a href='index.php'>🏠 返回主页</a></p>";
echo "<p><a href='admin/dashboard.php'>⚙️ 管理后台</a></p>";

// 显示修复说明
echo "<hr>";
echo "<h3>已修复的问题：</h3>";
echo "<ul>";
echo "<li>✅ 修复了数据库查询中的fetch模式问题</li>";
echo "<li>✅ 增加了文件上传大小限制配置（50MB）</li>";
echo "<li>✅ 添加了详细的调试信息</li>";
echo "<li>✅ 优化了微信小程序兼容性</li>";
echo "</ul>";

echo "<h3>如果仍有问题：</h3>";
echo "<ol>";
echo "<li>检查数据库中是否有数据</li>";
echo "<li>确认PDF文件是否正确上传到uploads/reports/目录</li>";
echo "<li>检查文件权限设置</li>";
echo "<li>查看服务器错误日志</li>";
echo "</ol>";
?>

<style>
body {
    font-family: Arial, sans-serif;
    max-width: 800px;
    margin: 0 auto;
    padding: 20px;
    line-height: 1.6;
}
h1, h2, h3 {
    color: #333;
}
a {
    color: #007bff;
    text-decoration: none;
}
a:hover {
    text-decoration: underline;
}
ul, ol {
    padding-left: 20px;
}
hr {
    margin: 20px 0;
    border: none;
    border-top: 1px solid #ddd;
}
</style>