<?php
require_once 'config/database.php';

echo "<h2>创建测试数据</h2>";

try {
    $database = new Database();
    $conn = $database->getConnection();
    
    // 检查是否已有测试数据
    $stmt = $conn->query("SELECT COUNT(*) as count FROM reports");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    
    if ($count > 0) {
        echo "<p>✅ 数据库中已有 {$count} 条记录</p>";
        
        // 显示现有数据
        $stmt = $conn->query("SELECT * FROM reports LIMIT 3");
        $reports = $stmt->fetchAll();
        
        echo "<h3>现有测试数据：</h3>";
        echo "<table border='1' style='border-collapse: collapse; margin: 10px 0;'>";
        echo "<tr style='background: #f0f0f0;'><th>ID</th><th>姓名</th><th>手机号</th><th>订单号</th><th>操作</th></tr>";
        foreach ($reports as $report) {
            echo "<tr>";
            echo "<td>{$report['id']}</td>";
            echo "<td>{$report['name']}</td>";
            echo "<td>{$report['phone']}</td>";
            echo "<td>{$report['order_number']}</td>";
            echo "<td>";
            echo "<a href='preview.php?id={$report['id']}' target='_blank' style='margin: 2px; padding: 5px 10px; background: #007bff; color: white; text-decoration: none; border-radius: 3px;'>预览</a>";
            echo "<a href='test_preview.php' target='_blank' style='margin: 2px; padding: 5px 10px; background: #28a745; color: white; text-decoration: none; border-radius: 3px;'>测试</a>";
            echo "</td>";
            echo "</tr>";
        }
        echo "</table>";
    } else {
        echo "<p>⚠️ 数据库中暂无数据，需要先上传报告文件</p>";
        echo "<p>请通过管理后台上传PDF文件来创建测试数据</p>";
    }
    
    // 创建测试用的简单PDF内容（如果uploads目录为空）
    $test_pdf_path = 'uploads/reports/test_report.pdf';
    if (!file_exists($test_pdf_path)) {
        // 创建目录
        if (!is_dir('uploads/reports')) {
            mkdir('uploads/reports', 0755, true);
        }
        
        // 创建一个简单的PDF文件（实际上是HTML，但用于测试）
        $test_content = "<!DOCTYPE html>
<html>
<head>
    <meta charset='UTF-8'>
    <title>测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; }
        .header { text-align: center; color: #333; }
        .content { margin: 20px 0; line-height: 1.6; }
    </style>
</head>
<body>
    <div class='header'>
        <h1>测试报告</h1>
        <p>这是一个用于测试PDF预览功能的示例文件</p>
    </div>
    <div class='content'>
        <h2>报告内容</h2>
        <p>这是测试报告的内容部分。</p>
        <p>用于验证PDF预览功能在不同环境下的兼容性。</p>
        <p>包括：</p>
        <ul>
            <li>微信环境兼容性</li>
            <li>安卓设备支持</li>
            <li>iOS设备支持</li>
            <li>普通浏览器支持</li>
        </ul>
        <h2>测试说明</h2>
        <p>如果您能看到这个内容，说明PDF预览功能正常工作。</p>
    </div>
</body>
</html>";
        
        file_put_contents($test_pdf_path, $test_content);
        echo "<p>✅ 创建了测试文件: {$test_pdf_path}</p>";
        
        // 如果数据库为空，插入测试数据
        if ($count == 0) {
            $stmt = $conn->prepare("INSERT INTO reports (name, phone, order_number, pdf_path, upload_time) VALUES (?, ?, ?, ?, ?)");
            $stmt->execute([
                '测试用户',
                '13800138000',
                'TEST001',
                $test_pdf_path,
                time()
            ]);
            
            echo "<p>✅ 插入了测试数据记录</p>";
        }
    }
    
    echo "<h3>快速测试链接：</h3>";
    echo "<div style='margin: 20px 0;'>";
    echo "<a href='test_preview.php' target='_blank' style='margin: 5px; padding: 10px 20px; background: #28a745; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>📊 功能测试页面</a>";
    echo "<a href='preview.php?id=1' target='_blank' style='margin: 5px; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>📄 智能预览</a>";
    echo "<a href='preview_simple.php?id=1' target='_blank' style='margin: 5px; padding: 10px 20px; background: #ffc107; color: #212529; text-decoration: none; border-radius: 5px; display: inline-block;'>🔧 简化预览</a>";
    echo "<a href='preview_pdfjs.php?id=1' target='_blank' style='margin: 5px; padding: 10px 20px; background: #17a2b8; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>📚 PDF.js预览</a>";
    echo "<a href='wechat_help.php' target='_blank' style='margin: 5px; padding: 10px 20px; background: #6c757d; color: white; text-decoration: none; border-radius: 5px; display: inline-block;'>❓ 帮助页面</a>";
    echo "</div>";
    
    echo "<h3>环境信息：</h3>";
    echo "<div style='background: #f8f9fa; padding: 15px; border-radius: 5px; margin: 10px 0;'>";
    echo "<strong>服务器信息：</strong><br>";
    echo "PHP版本: " . PHP_VERSION . "<br>";
    echo "服务器软件: " . ($_SERVER['SERVER_SOFTWARE'] ?? 'Unknown') . "<br>";
    echo "文档根目录: " . $_SERVER['DOCUMENT_ROOT'] . "<br>";
    echo "当前目录: " . __DIR__ . "<br>";
    echo "</div>";
    
    echo "<script>";
    echo "document.addEventListener('DOMContentLoaded', function() {";
    echo "    const ua = navigator.userAgent;";
    echo "    let envInfo = '客户端信息：<br>';";
    echo "    if (ua.indexOf('MicroMessenger') > -1) {";
    echo "        envInfo += '✅ 微信环境<br>';";
    echo "        if (ua.indexOf('Android') > -1) envInfo += '🤖 安卓系统<br>';";
    echo "        if (ua.indexOf('iPhone') > -1 || ua.indexOf('iPad') > -1) envInfo += '📱 iOS系统<br>';";
    echo "    } else {";
    echo "        envInfo += '🌐 普通浏览器<br>';";
    echo "    }";
    echo "    envInfo += '用户代理: ' + ua;";
    echo "    document.body.innerHTML += '<div style=\"background: #e3f2fd; padding: 15px; border-radius: 5px; margin: 10px 0;\"><strong>' + envInfo + '</strong></div>';";
    echo "});";
    echo "</script>";
    
} catch (Exception $e) {
    echo "<p style='color: red;'>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回主页</a> | <a href='admin/dashboard.php'>管理后台</a> | <a href='debug_reports.php'>调试页面</a></p>";
?>
