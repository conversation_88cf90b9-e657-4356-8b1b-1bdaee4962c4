<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>PDF预览功能测试</title>
    <style>
        body {
            margin: 0;
            padding: 20px;
            font-family: Arial, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            padding: 30px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        .header {
            text-align: center;
            margin-bottom: 30px;
            padding-bottom: 20px;
            border-bottom: 2px solid #eee;
        }
        .test-section {
            margin: 25px 0;
            padding: 20px;
            background: #f8f9fa;
            border-radius: 10px;
            border-left: 4px solid #667eea;
        }
        .test-section h3 {
            margin: 0 0 15px 0;
            color: #333;
        }
        .test-info {
            background: #e3f2fd;
            border: 1px solid #bbdefb;
            color: #1565c0;
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-size: 14px;
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 8px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success { background: linear-gradient(135deg, #28a745 0%, #20c997 100%); }
        .btn-warning { background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%); color: #212529; }
        .btn-info { background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%); }
        .btn-secondary { background: linear-gradient(135deg, #6c757d 0%, #495057 100%); }
        
        .status {
            padding: 10px;
            border-radius: 5px;
            margin: 10px 0;
            font-weight: bold;
        }
        .status.success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .status.warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .status.error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .status.info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        
        .grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
            margin: 20px 0;
        }
        
        @media (max-width: 768px) {
            .container {
                margin: 10px;
                padding: 20px;
            }
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>📄 PDF预览功能测试</h1>
            <p>测试不同环境下的PDF预览兼容性</p>
        </div>
        
        <div class="test-info" id="environmentInfo">
            <strong>🔍 当前环境检测</strong><br>
            <span id="envDetails">正在检测...</span>
        </div>
        
        <div class="test-section">
            <h3>🧪 预览方案测试</h3>
            <p>测试各种PDF预览方案在当前环境下的兼容性：</p>
            
            <div class="grid">
                <a href="preview.php?id=1" class="btn btn-success" target="_blank">
                    📱 智能预览
                </a>
                <a href="preview_simple.php?id=1" class="btn btn-warning" target="_blank">
                    🔧 简化预览
                </a>
                <a href="preview_pdfjs.php?id=1" class="btn btn-info" target="_blank">
                    📚 PDF.js预览
                </a>
                <a href="wechat_help.php" class="btn btn-secondary" target="_blank">
                    ❓ 帮助页面
                </a>
            </div>
            
            <div id="testResults">
                <h4>测试结果记录：</h4>
                <div id="resultsList">
                    <p style="color: #666;">点击上方按钮开始测试，结果将显示在这里...</p>
                </div>
            </div>
        </div>
        
        <div class="test-section">
            <h3>📋 测试检查清单</h3>
            <div style="text-align: left;">
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check1"> 智能预览页面能正常加载
                </label>
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check2"> 环境检测功能正常工作
                </label>
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check3"> 预览选项按钮都能点击
                </label>
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check4"> 错误提示信息清晰明确
                </label>
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check5"> 帮助页面内容完整
                </label>
                <label style="display: block; margin: 10px 0;">
                    <input type="checkbox" id="check6"> 在微信中能正常使用
                </label>
            </div>
            
            <button onclick="generateReport()" class="btn btn-success" style="margin-top: 15px;">
                📊 生成测试报告
            </button>
        </div>
        
        <div class="test-section">
            <h3>🔧 问题排查</h3>
            <div id="troubleshooting">
                <p><strong>常见问题及解决方案：</strong></p>
                <ul>
                    <li><strong>安卓微信无法预览：</strong> 这是已知问题，已提供多种替代方案</li>
                    <li><strong>PDF.js加载慢：</strong> 网络问题，可尝试简化预览或直接下载</li>
                    <li><strong>页面显示异常：</strong> 检查浏览器兼容性，建议使用现代浏览器</li>
                    <li><strong>下载功能异常：</strong> 检查文件路径和权限设置</li>
                </ul>
            </div>
        </div>
        
        <div style="text-align: center; margin-top: 30px;">
            <a href="index.php" class="btn btn-success">🏠 返回首页</a>
            <a href="admin/dashboard.php" class="btn btn-info">⚙️ 管理后台</a>
        </div>
    </div>
    
    <script>
        // 环境检测
        function detectEnvironment() {
            const ua = navigator.userAgent;
            let env = [];
            
            if (ua.indexOf('MicroMessenger') > -1) {
                env.push('✅ 微信环境');
                
                if (ua.indexOf('Android') > -1) {
                    env.push('🤖 安卓系统');
                } else if (ua.indexOf('iPhone') > -1 || ua.indexOf('iPad') > -1) {
                    env.push('📱 iOS系统');
                }
                
                const wechatMatch = ua.match(/MicroMessenger\/([0-9\.]+)/);
                if (wechatMatch) {
                    env.push('微信版本: ' + wechatMatch[1]);
                }
            } else {
                env.push('🌐 普通浏览器');
                
                if (ua.indexOf('Chrome') > -1) env.push('Chrome');
                else if (ua.indexOf('Safari') > -1) env.push('Safari');
                else if (ua.indexOf('Firefox') > -1) env.push('Firefox');
                else if (ua.indexOf('Edge') > -1) env.push('Edge');
            }
            
            if (ua.indexOf('Mobile') > -1) {
                env.push('📱 移动设备');
            } else {
                env.push('💻 桌面设备');
            }
            
            return env.join(' | ');
        }
        
        // 生成测试报告
        function generateReport() {
            const checks = [];
            for (let i = 1; i <= 6; i++) {
                const checkbox = document.getElementById('check' + i);
                checks.push({
                    item: checkbox.parentElement.textContent.trim(),
                    passed: checkbox.checked
                });
            }
            
            const passed = checks.filter(c => c.passed).length;
            const total = checks.length;
            const percentage = Math.round((passed / total) * 100);
            
            let report = `📊 测试报告\n`;
            report += `环境: ${detectEnvironment()}\n`;
            report += `时间: ${new Date().toLocaleString()}\n`;
            report += `通过率: ${passed}/${total} (${percentage}%)\n\n`;
            report += `详细结果:\n`;
            
            checks.forEach((check, index) => {
                report += `${index + 1}. ${check.passed ? '✅' : '❌'} ${check.item}\n`;
            });
            
            alert(report);
        }
        
        // 页面加载完成后执行
        document.addEventListener('DOMContentLoaded', function() {
            document.getElementById('envDetails').textContent = detectEnvironment();
        });
    </script>
</body>
</html>
