# 报告查询系统 - Apache配置文件

# 启用重写引擎
RewriteEngine On

# 文件上传大小限制设置
php_value upload_max_filesize 50M
php_value post_max_size 50M
php_value max_execution_time 300
php_value max_input_time 300
php_value memory_limit 256M

# 防止直接访问敏感文件
<Files "database.sql">
    Order Allow,Deny
    Deny from all
</Files>

<Files "README.md">
    Order Allow,Deny
    Deny from all
</Files>

<Files ".htaccess">
    Order Allow,Deny
    Deny from all
</Files>

# 防止访问配置目录
<Directory "config">
    Order Allow,Deny
    Deny from all
</Directory>

# 设置上传文件的安全头
<Directory "uploads">
    # 防止执行PHP文件
    <Files "*.php">
        Order Allow,Deny
        Deny from all
    </Files>
    
    # 设置PDF文件的正确MIME类型
    <Files "*.pdf">
        Header set Content-Type "application/pdf"
        Header set Content-Disposition "inline"
    </Files>
</Directory>

# 安全头设置
Header always set X-Content-Type-Options nosniff
# 允许在iframe中显示，支持微信小程序webview
Header always set X-Frame-Options SAMEORIGIN
Header always set X-XSS-Protection "1; mode=block"

# 微信小程序支持
<IfModule mod_rewrite.c>
    # 检测微信用户代理
    RewriteCond %{HTTP_USER_AGENT} MicroMessenger [NC]
    RewriteRule ^uploads/(.*)$ uploads/$1 [L,E=WECHAT:1]
    
    # 为微信环境设置特殊头部
    Header always set Cache-Control "no-cache, no-store, must-revalidate" env=WECHAT
    Header always set Pragma "no-cache" env=WECHAT
    Header always set Expires "0" env=WECHAT
</IfModule>

# 缓存设置
<IfModule mod_expires.c>
    ExpiresActive On
    ExpiresByType text/css "access plus 1 month"
    ExpiresByType application/javascript "access plus 1 month"
    ExpiresByType image/png "access plus 1 month"
    ExpiresByType image/jpg "access plus 1 month"
    ExpiresByType image/jpeg "access plus 1 month"
    ExpiresByType image/gif "access plus 1 month"
    ExpiresByType image/svg+xml "access plus 1 month"
</IfModule>

# 压缩设置
<IfModule mod_deflate.c>
    AddOutputFilterByType DEFLATE text/plain
    AddOutputFilterByType DEFLATE text/html
    AddOutputFilterByType DEFLATE text/xml
    AddOutputFilterByType DEFLATE text/css
    AddOutputFilterByType DEFLATE application/xml
    AddOutputFilterByType DEFLATE application/xhtml+xml
    AddOutputFilterByType DEFLATE application/rss+xml
    AddOutputFilterByType DEFLATE application/javascript
    AddOutputFilterByType DEFLATE application/x-javascript
</IfModule>