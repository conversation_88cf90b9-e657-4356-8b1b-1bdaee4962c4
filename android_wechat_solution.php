<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        die('报告不存在');
    }
    
    $pdf_path = $report['pdf_path'];
    
    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }
    
    // 环境检测
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    $is_android = strpos($user_agent, 'Android') !== false;
    
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>安卓微信专用解决方案</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 10px;
        }
        .container {
            max-width: 400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            overflow: hidden;
            box-shadow: 0 10px 30px rgba(0,0,0,0.2);
        }
        .header {
            background: linear-gradient(135deg, #ff6b6b 0%, #ee5a24 100%);
            color: white;
            padding: 20px;
            text-align: center;
        }
        .header h1 {
            font-size: 18px;
            margin-bottom: 5px;
        }
        .header p {
            font-size: 14px;
            opacity: 0.9;
        }
        .content {
            padding: 25px;
        }
        .alert {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 8px;
            margin-bottom: 20px;
            font-size: 14px;
            line-height: 1.5;
        }
        .alert strong {
            display: block;
            margin-bottom: 8px;
        }
        .solution-card {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            border-radius: 10px;
            padding: 20px;
            margin: 15px 0;
            position: relative;
        }
        .solution-card h3 {
            color: #495057;
            font-size: 16px;
            margin-bottom: 10px;
            display: flex;
            align-items: center;
        }
        .solution-card .icon {
            font-size: 20px;
            margin-right: 8px;
        }
        .steps {
            list-style: none;
            padding: 0;
            counter-reset: step-counter;
        }
        .steps li {
            counter-increment: step-counter;
            padding: 10px 0;
            padding-left: 35px;
            position: relative;
            border-bottom: 1px solid #eee;
            font-size: 14px;
            line-height: 1.4;
        }
        .steps li:last-child {
            border-bottom: none;
        }
        .steps li:before {
            content: counter(step-counter);
            position: absolute;
            left: 0;
            top: 10px;
            background: #667eea;
            color: white;
            width: 22px;
            height: 22px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }
        .btn {
            display: block;
            width: 100%;
            padding: 15px;
            margin: 10px 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            text-align: center;
            font-size: 16px;
            font-weight: bold;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        .btn-warning {
            background: linear-gradient(135deg, #ffc107 0%, #fd7e14 100%);
            color: #212529;
        }
        .btn-info {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
        }
        .btn-secondary {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
        }
        .highlight {
            background: #e3f2fd;
            border-left: 4px solid #2196f3;
            padding: 15px;
            margin: 15px 0;
            border-radius: 0 8px 8px 0;
        }
        .highlight strong {
            color: #1565c0;
        }
        .copy-text {
            background: #f8f9fa;
            border: 1px solid #dee2e6;
            padding: 10px;
            border-radius: 5px;
            font-family: monospace;
            font-size: 12px;
            margin: 10px 0;
            word-break: break-all;
        }
        .footer {
            background: #f8f9fa;
            padding: 20px;
            text-align: center;
            border-top: 1px solid #dee2e6;
        }
        @media (max-width: 480px) {
            .container {
                margin: 5px;
                border-radius: 10px;
            }
            .content {
                padding: 20px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 安卓微信专用解决方案</h1>
            <p>为安卓微信环境特别优化的PDF查看方案</p>
        </div>
        
        <div class="content">
            <div class="alert">
                <strong>⚠️ 重要提示</strong>
                安卓微信的WebView对PDF支持非常有限，这是微信的技术限制，不是我们系统的问题。以下提供几种可行的解决方案。
            </div>
            
            <!-- 方案1：复制链接到浏览器 -->
            <div class="solution-card">
                <h3><span class="icon">🔗</span>方案一：复制链接到浏览器（推荐）</h3>
                <ol class="steps">
                    <li>长按下方链接，选择"复制链接"</li>
                    <li>打开手机浏览器（如Chrome、UC浏览器等）</li>
                    <li>粘贴链接并访问</li>
                    <li>在浏览器中正常预览或下载PDF</li>
                </ol>
                <div class="copy-text" id="pdfLink">
                    <?php echo 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $pdf_path; ?>
                </div>
                <button onclick="copyLink()" class="btn btn-info">📋 复制PDF链接</button>
            </div>
            
            <!-- 方案2：微信内置浏览器 -->
            <div class="solution-card">
                <h3><span class="icon">🌐</span>方案二：使用微信内置浏览器</h3>
                <ol class="steps">
                    <li>点击右上角"..."菜单</li>
                    <li>选择"在浏览器中打开"</li>
                    <li>等待页面在浏览器中加载</li>
                    <li>在浏览器中查看PDF文件</li>
                </ol>
                <a href="<?php echo htmlspecialchars($pdf_path); ?>" class="btn btn-warning" target="_blank">
                    🔗 直接打开PDF文件
                </a>
            </div>
            
            <!-- 方案3：QQ浏览器 -->
            <div class="solution-card">
                <h3><span class="icon">📱</span>方案三：使用QQ浏览器</h3>
                <ol class="steps">
                    <li>安装QQ浏览器（如果没有的话）</li>
                    <li>复制上方PDF链接</li>
                    <li>在QQ浏览器中打开链接</li>
                    <li>QQ浏览器对PDF支持较好</li>
                </ol>
                <button onclick="openInQQBrowser()" class="btn btn-secondary">
                    🌐 尝试QQ浏览器打开
                </button>
            </div>
            
            <!-- 方案4：下载到本地 -->
            <div class="solution-card">
                <h3><span class="icon">💾</span>方案四：下载到本地查看</h3>
                <ol class="steps">
                    <li>点击下方下载按钮</li>
                    <li>选择保存位置（通常是下载文件夹）</li>
                    <li>下载完成后，在文件管理器中找到文件</li>
                    <li>使用PDF阅读器打开（如WPS、Adobe Reader等）</li>
                </ol>
                <a href="download.php?id=<?php echo htmlspecialchars($report_id); ?>&force=1" class="btn btn-success">
                    💾 强制下载PDF文件
                </a>
            </div>
            
            <div class="highlight">
                <strong>💡 温馨提示：</strong>
                如果以上方案都无法使用，请联系客服获取帮助。我们也可以通过其他方式（如邮件）发送PDF文件给您。
            </div>
            
            <!-- 报告信息 -->
            <div style="background: #e8f5e8; padding: 15px; border-radius: 8px; margin: 20px 0;">
                <h4 style="margin: 0 0 10px 0; color: #155724;">📄 报告信息</h4>
                <p style="margin: 5px 0; color: #155724;"><strong>姓名：</strong><?php echo htmlspecialchars($report['name']); ?></p>
                <p style="margin: 5px 0; color: #155724;"><strong>手机号：</strong><?php echo htmlspecialchars($report['phone']); ?></p>
                <p style="margin: 5px 0; color: #155724;"><strong>订单号：</strong><?php echo htmlspecialchars($report['order_number']); ?></p>
            </div>
        </div>
        
        <div class="footer">
            <a href="javascript:history.back()" class="btn" style="width: auto; display: inline-block; padding: 10px 20px;">
                ← 返回上一页
            </a>
        </div>
    </div>
    
    <script>
        function copyLink() {
            const linkText = document.getElementById('pdfLink').textContent;
            
            // 尝试使用现代API
            if (navigator.clipboard) {
                navigator.clipboard.writeText(linkText).then(function() {
                    alert('✅ 链接已复制到剪贴板！\n请打开浏览器粘贴访问。');
                }).catch(function() {
                    fallbackCopy(linkText);
                });
            } else {
                fallbackCopy(linkText);
            }
        }
        
        function fallbackCopy(text) {
            // 备用复制方法
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            
            try {
                document.execCommand('copy');
                alert('✅ 链接已复制！\n请打开浏览器粘贴访问。');
            } catch (err) {
                alert('❌ 复制失败，请手动复制链接：\n' + text);
            }
            
            document.body.removeChild(textArea);
        }
        
        function openInQQBrowser() {
            const pdfUrl = document.getElementById('pdfLink').textContent;
            const qqBrowserUrl = 'mttbrowser://url=' + encodeURIComponent(pdfUrl);
            
            // 尝试打开QQ浏览器
            window.location.href = qqBrowserUrl;
            
            // 如果QQ浏览器没有安装，提示用户
            setTimeout(function() {
                if (confirm('如果没有自动打开QQ浏览器，是否要复制链接手动打开？')) {
                    copyLink();
                }
            }, 2000);
        }
        
        // 页面加载完成后的提示
        document.addEventListener('DOMContentLoaded', function() {
            // 检测环境并给出建议
            const ua = navigator.userAgent;
            if (ua.indexOf('MicroMessenger') > -1 && ua.indexOf('Android') > -1) {
                setTimeout(function() {
                    if (confirm('检测到您在安卓微信中访问。\n建议使用"复制链接到浏览器"的方式查看PDF。\n是否现在复制链接？')) {
                        copyLink();
                    }
                }, 1000);
            }
        });
    </script>
</body>
</html>

<?php
} catch (Exception $e) {
    die('查询出错：' . $e->getMessage());
}
?>
