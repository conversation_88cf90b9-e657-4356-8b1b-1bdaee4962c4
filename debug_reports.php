<?php
require_once 'config/database.php';

// 调试脚本 - 检查报告数据
try {
    $database = new Database();
    $conn = $database->getConnection();
    
    echo "<h2>数据库连接测试</h2>";
    echo "<p>✅ 数据库连接成功</p>";
    
    // 检查reports表是否存在
    $stmt = $conn->query("SHOW TABLES LIKE 'reports'");
    if ($stmt->rowCount() > 0) {
        echo "<p>✅ reports表存在</p>";
    } else {
        echo "<p>❌ reports表不存在</p>";
        exit;
    }
    
    // 检查表结构
    echo "<h3>表结构：</h3>";
    $stmt = $conn->query("DESCRIBE reports");
    $columns = $stmt->fetchAll();
    echo "<table border='1' style='border-collapse: collapse;'>";
    echo "<tr><th>字段名</th><th>类型</th><th>是否为空</th><th>键</th><th>默认值</th></tr>";
    foreach ($columns as $column) {
        echo "<tr>";
        echo "<td>{$column['Field']}</td>";
        echo "<td>{$column['Type']}</td>";
        echo "<td>{$column['Null']}</td>";
        echo "<td>{$column['Key']}</td>";
        echo "<td>{$column['Default']}</td>";
        echo "</tr>";
    }
    echo "</table>";
    
    // 检查数据数量
    $stmt = $conn->query("SELECT COUNT(*) as count FROM reports");
    $count = $stmt->fetch(PDO::FETCH_ASSOC)['count'];
    echo "<h3>数据统计：</h3>";
    echo "<p>总报告数量: {$count}</p>";
    
    if ($count > 0) {
        // 显示前5条数据
        echo "<h3>前5条数据：</h3>";
        $stmt = $conn->query("SELECT * FROM reports LIMIT 5");
        $reports = $stmt->fetchAll();
        
        echo "<table border='1' style='border-collapse: collapse;'>";
        echo "<tr><th>ID</th><th>姓名</th><th>手机号</th><th>订单号</th><th>PDF路径</th><th>上传时间</th></tr>";
        foreach ($reports as $report) {
            echo "<tr>";
            echo "<td>{$report['id']}</td>";
            echo "<td>{$report['name']}</td>";
            echo "<td>{$report['phone']}</td>";
            echo "<td>{$report['order_number']}</td>";
            echo "<td>{$report['pdf_path']}</td>";
            echo "<td>" . date('Y-m-d H:i:s', $report['upload_time']) . "</td>";
            echo "</tr>";
        }
        echo "</table>";
        
        // 检查文件是否存在
        echo "<h3>文件存在性检查：</h3>";
        foreach ($reports as $report) {
            $file_path = $report['pdf_path'];
            $exists = file_exists($file_path) ? '✅' : '❌';
            echo "<p>{$exists} {$file_path}</p>";
        }
    }
    
    // 测试URL参数
    echo "<h3>URL参数测试：</h3>";
    if (isset($_GET['test_id'])) {
        $test_id = $_GET['test_id'];
        echo "<p>测试ID: {$test_id}</p>";
        
        $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
        $stmt->execute([$test_id]);
        $report = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($report) {
            echo "<p>✅ 找到报告: {$report['name']} - {$report['order_number']}</p>";
            echo "<p>PDF路径: {$report['pdf_path']}</p>";
            echo "<p>文件存在: " . (file_exists($report['pdf_path']) ? '是' : '否') . "</p>";
        } else {
            echo "<p>❌ 未找到ID为 {$test_id} 的报告</p>";
        }
    } else {
        echo "<p>在URL后添加 ?test_id=1 来测试特定ID的查询</p>";
    }
    
} catch (Exception $e) {
    echo "<p>❌ 错误: " . $e->getMessage() . "</p>";
}

echo "<hr>";
echo "<p><a href='index.php'>返回主页</a> | <a href='admin/dashboard.php'>管理后台</a></p>";
?>