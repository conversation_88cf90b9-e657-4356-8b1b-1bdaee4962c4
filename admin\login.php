<?php
session_start();

require_once '../config/database.php';
require_once '../config/captcha.php';

// 检查是否已经登录
if (isset($_SESSION['admin_logged_in']) && $_SESSION['admin_logged_in']) {
    header('Location: dashboard.php');
    exit();
}

$error_message = '';

// 获取客户端IP地址
function getClientIP() {
    if (!empty($_SERVER['HTTP_CLIENT_IP'])) {
        return $_SERVER['HTTP_CLIENT_IP'];
    } elseif (!empty($_SERVER['HTTP_X_FORWARDED_FOR'])) {
        return $_SERVER['HTTP_X_FORWARDED_FOR'];
    } else {
        return $_SERVER['REMOTE_ADDR'];
    }
}

$client_ip = getClientIP();

// 初始化IP登录失败记录
if (!isset($_SESSION['ip_login_attempts'])) {
    $_SESSION['ip_login_attempts'] = [];
}

// 清理过期的IP记录（超过1小时）
foreach ($_SESSION['ip_login_attempts'] as $ip => $data) {
    if (time() - $data['last_attempt'] > 3600) { // 1小时 = 3600秒
        unset($_SESSION['ip_login_attempts'][$ip]);
    }
}

// 检查当前IP的登录失败次数
$show_captcha = false;
$ip_blocked = false;

if (isset($_SESSION['ip_login_attempts'][$client_ip])) {
    $ip_data = $_SESSION['ip_login_attempts'][$client_ip];
    
    // 如果失败次数超过5次且在1小时内，则禁止登录
    if ($ip_data['attempts'] >= 5 && time() - $ip_data['last_attempt'] < 3600) {
        $ip_blocked = true;
        $remaining_time = 3600 - (time() - $ip_data['last_attempt']);
        $remaining_minutes = ceil($remaining_time / 60);
        $error_message = "该IP地址登录失败次数过多，请{$remaining_minutes}分钟后再试";
    } elseif ($ip_data['attempts'] >= 2) {
        $show_captcha = true;
    }
}

if ($_SERVER['REQUEST_METHOD'] === 'POST' && !$ip_blocked) {
    $username = trim($_POST['username'] ?? '');
    $password = $_POST['password'] ?? '';
    $captcha = $_POST['captcha'] ?? '';
    
    // 验证验证码（如果需要）
    $captcha_valid = true;
    if ($show_captcha) {
        $captcha_valid = Captcha::verify($captcha);
        if (!$captcha_valid) {
            $error_message = '验证码错误';
        }
    }
    
    if (!$ip_blocked && !empty($username) && !empty($password) && $captcha_valid) {
        $database = new Database();
        $conn = $database->getConnection();
        
        $stmt = $conn->prepare("SELECT id, username, password FROM admins WHERE username = ?");
        $stmt->execute([$username]);
        $admin = $stmt->fetch();
        
        if ($admin && password_verify($password, $admin['password'])) {
            // 登录成功，清除该IP的失败记录
            if (isset($_SESSION['ip_login_attempts'][$client_ip])) {
                unset($_SESSION['ip_login_attempts'][$client_ip]);
            }
            $_SESSION['admin_logged_in'] = true;
            $_SESSION['admin_id'] = $admin['id'];
            $_SESSION['admin_username'] = $admin['username'];
            header('Location: dashboard.php');
            exit();
        } else {
            // 登录失败，记录IP失败次数
            if (!isset($_SESSION['ip_login_attempts'][$client_ip])) {
                $_SESSION['ip_login_attempts'][$client_ip] = [
                    'attempts' => 0,
                    'last_attempt' => time()
                ];
            }
            
            $_SESSION['ip_login_attempts'][$client_ip]['attempts']++;
            $_SESSION['ip_login_attempts'][$client_ip]['last_attempt'] = time();
            
            $error_message = '用户名或密码错误';
            
            // 检查是否需要显示验证码
            if ($_SESSION['ip_login_attempts'][$client_ip]['attempts'] >= 2) {
                $show_captcha = true;
            }
        }
    } else if (empty($username) || empty($password)) {
        $error_message = '请填写用户名和密码';
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理员登录 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .login-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        .login-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .login-body {
            padding: 2rem;
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-login {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-login:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .input-group-text {
            background: #f8f9fa;
            border: 2px solid #e9ecef;
            border-right: none;
        }
        .input-group .form-control {
            border-left: none;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-4">
                <div class="login-container">
                    <div class="login-header">
                        <i class="fas fa-user-shield fa-3x mb-3"></i>
                        <h3>管理员登录</h3>
                        <p class="mb-0">报告查询系统后台</p>
                    </div>
                    <div class="login-body">
                        <?php if ($error_message): ?>
                            <div class="alert alert-danger" role="alert">
                                <i class="fas fa-exclamation-triangle me-2"></i>
                                <?php echo htmlspecialchars($error_message); ?>
                            </div>
                        <?php endif; ?>
                        
                        <form method="POST">
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-user"></i>
                                    </span>
                                    <input type="text" class="form-control" name="username" placeholder="用户名" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="input-group">
                                    <span class="input-group-text">
                                        <i class="fas fa-lock"></i>
                                    </span>
                                    <input type="password" class="form-control" name="password" placeholder="密码" required>
                                </div>
                            </div>
                            
                            <?php if ($show_captcha): ?>
                            <div class="mb-4">
                                <label for="captcha" class="form-label">
                                    <i class="fas fa-shield-alt me-1"></i>
                                    验证码
                                </label>
                                <div class="row">
                                    <div class="col-6">
                                        <input type="text" class="form-control" name="captcha" placeholder="验证码" required>
                                    </div>
                                    <div class="col-6">
                                        <img src="../captcha_image.php" alt="验证码" class="img-fluid" 
                                             style="height: 38px; cursor: pointer; border-radius: 5px;" 
                                             onclick="this.src='../captcha_image.php?'+Math.random()" 
                                             title="点击刷新验证码">
                                    </div>
                                </div>
                                <small class="text-muted">点击图片刷新验证码</small>
                            </div>
                            <?php endif; ?>
                            
                            <button type="submit" class="btn btn-primary btn-login w-100" 
                                    <?php echo $ip_blocked ? 'disabled' : ''; ?>>
                                <i class="fas fa-sign-in-alt me-2"></i>
                                登录
                            </button>
                        </form>
                        

                        
                        <div class="text-center mt-3">
                            <a href="../index.php" class="text-decoration-none">
                                <i class="fas fa-arrow-left me-1"></i>
                                返回用户查询页面
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>