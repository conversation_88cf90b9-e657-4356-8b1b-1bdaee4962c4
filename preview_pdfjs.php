<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        die('报告不存在');
    }
    
    $pdf_path = $report['pdf_path'];
    
    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }
    
    // 构建PDF URL
    $pdf_url = 'http://' . $_SERVER['HTTP_HOST'] . dirname($_SERVER['REQUEST_URI']) . '/' . $pdf_path;
    
    // 环境检测
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    $is_android = strpos($user_agent, 'Android') !== false;
    
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告预览 - PDF.js</title>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: Arial, sans-serif;
            background-color: #f5f5f5;
        }
        .container {
            max-width: 100%;
            margin: 0 auto;
            background: white;
            min-height: 100vh;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px;
            text-align: center;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .content {
            padding: 0;
        }
        .pdf-viewer {
            width: 100%;
            height: calc(100vh - 120px);
            border: none;
            display: block;
        }
        .loading {
            text-align: center;
            padding: 50px;
            color: #666;
        }
        .error-message {
            background: #f8d7da;
            border: 1px solid #f5c6cb;
            color: #721c24;
            padding: 20px;
            margin: 20px;
            border-radius: 8px;
            text-align: center;
        }
        .controls {
            position: fixed;
            bottom: 20px;
            left: 50%;
            transform: translateX(-50%);
            background: rgba(255,255,255,0.9);
            padding: 10px;
            border-radius: 25px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.2);
            backdrop-filter: blur(10px);
        }
        .btn {
            display: inline-block;
            padding: 12px 24px;
            margin: 5px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            text-decoration: none;
            border-radius: 8px;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .btn-success {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
        }
        @media (max-width: 768px) {
            .header {
                padding: 10px;
            }
            .header h2 {
                font-size: 1.2rem;
                margin: 0;
            }
            .header p {
                font-size: 0.9rem;
                margin: 5px 0 0 0;
            }
            .pdf-viewer {
                height: calc(100vh - 100px);
            }
            .controls {
                bottom: 10px;
                padding: 8px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>📄 报告预览 (PDF.js)</h2>
            <p>姓名：<?php echo htmlspecialchars($report['name']); ?> | 订单号：<?php echo htmlspecialchars($report['order_number']); ?></p>
        </div>
        
        <div class="content">
            <div class="loading" id="loading">
                <div style="font-size: 2rem;">📄</div>
                正在使用PDF.js加载文件...
            </div>
            
            <iframe id="pdfViewer" 
                    src="https://mozilla.github.io/pdf.js/web/viewer.html?file=<?php echo urlencode($pdf_url); ?>" 
                    class="pdf-viewer" 
                    style="display:none;">
            </iframe>
            
            <div id="errorMessage" class="error-message" style="display:none;">
                <h4>❌ PDF.js 预览失败</h4>
                <p>无法使用PDF.js加载文件。</p>
                <p><strong>可能的原因：</strong></p>
                <ul style="text-align: left; display: inline-block;">
                    <li>网络连接问题</li>
                    <li>PDF文件损坏</li>
                    <li>浏览器不支持</li>
                </ul>
                <a href="preview.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn">返回标准预览</a>
                <a href="download.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn btn-success">下载文件</a>
            </div>
        </div>
        
        <div class="controls">
            <a href="preview.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn">
                ← 标准预览
            </a>
            <a href="download.php?id=<?php echo htmlspecialchars($report_id); ?>" class="btn btn-success">
                💾 下载
            </a>
        </div>
    </div>
    
    <script>
        function showPDF() {
            document.getElementById("loading").style.display = "none";
            document.getElementById("pdfViewer").style.display = "block";
        }
        
        function showError() {
            document.getElementById("loading").style.display = "none";
            document.getElementById("errorMessage").style.display = "block";
        }
        
        document.addEventListener("DOMContentLoaded", function() {
            const iframe = document.getElementById("pdfViewer");
            
            // 监听iframe加载
            iframe.onload = function() {
                setTimeout(showPDF, 500);
            };
            
            iframe.onerror = function() {
                showError();
            };
            
            // 设置超时
            setTimeout(function() {
                if (iframe.style.display === "none") {
                    showError();
                }
            }, 10000); // 10秒超时
        });
    </script>
</body>
</html>

<?php
} catch (Exception $e) {
    die('查询出错：' . $e->getMessage());
}
?>
