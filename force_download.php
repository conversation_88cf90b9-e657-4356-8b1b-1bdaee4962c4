<?php
require_once 'config/database.php';

// 获取报告ID
$report_id = $_GET['id'] ?? '';

if (empty($report_id)) {
    die('报告ID不能为空');
}

// 查询报告信息
$database = new Database();
$conn = $database->getConnection();

try {
    $stmt = $conn->prepare("SELECT * FROM reports WHERE id = ?");
    $stmt->execute([$report_id]);
    $report = $stmt->fetch(PDO::FETCH_ASSOC);
    
    if (!$report) {
        die('报告不存在');
    }
    
    $pdf_path = $report['pdf_path'];
    
    // 检查文件是否存在
    if (!file_exists($pdf_path)) {
        die('报告文件不存在');
    }
    
    // 生成安全的文件名
    $safe_filename = '报告_' . preg_replace('/[^\w\-_\.]/', '_', $report['name']) . '_' . preg_replace('/[^\w\-_\.]/', '_', $report['order_number']) . '.pdf';
    
    // 检测是否是直接下载请求
    if (isset($_GET['action']) && $_GET['action'] === 'download') {
        // 强制下载文件
        
        // 清除所有输出缓冲
        while (ob_get_level()) {
            ob_end_clean();
        }
        
        // 设置下载头信息
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment; filename="' . $safe_filename . '"');
        header('Content-Transfer-Encoding: binary');
        header('Content-Length: ' . filesize($pdf_path));
        header('Cache-Control: must-revalidate, post-check=0, pre-check=0');
        header('Pragma: public');
        header('Expires: 0');
        
        // 防止缓存
        header('Last-Modified: ' . gmdate('D, d M Y H:i:s') . ' GMT');
        header('ETag: "' . md5_file($pdf_path) . '"');
        
        // 输出文件内容
        $file = fopen($pdf_path, 'rb');
        if ($file) {
            while (!feof($file)) {
                echo fread($file, 8192);
                flush();
            }
            fclose($file);
        } else {
            die('无法读取文件');
        }
        exit;
    }
    
    // 环境检测
    $user_agent = $_SERVER['HTTP_USER_AGENT'] ?? '';
    $is_wechat = strpos($user_agent, 'MicroMessenger') !== false;
    $is_android = strpos($user_agent, 'Android') !== false;
    
?>
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>强制下载 - <?php echo htmlspecialchars($report['name']); ?></title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'PingFang SC', sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            padding: 20px;
        }
        .container {
            background: white;
            border-radius: 15px;
            padding: 40px;
            max-width: 500px;
            width: 100%;
            text-align: center;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
        }
        .icon {
            font-size: 64px;
            margin-bottom: 20px;
            animation: bounce 2s infinite;
        }
        @keyframes bounce {
            0%, 20%, 50%, 80%, 100% { transform: translateY(0); }
            40% { transform: translateY(-10px); }
            60% { transform: translateY(-5px); }
        }
        h1 {
            color: #333;
            margin-bottom: 10px;
            font-size: 24px;
        }
        .subtitle {
            color: #666;
            margin-bottom: 30px;
            font-size: 16px;
        }
        .file-info {
            background: #f8f9fa;
            border-radius: 10px;
            padding: 20px;
            margin: 20px 0;
            text-align: left;
        }
        .file-info h3 {
            color: #495057;
            margin-bottom: 15px;
            font-size: 18px;
        }
        .file-info p {
            margin: 8px 0;
            color: #6c757d;
        }
        .file-info strong {
            color: #495057;
        }
        .download-btn {
            display: inline-block;
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            color: white;
            padding: 15px 30px;
            border-radius: 50px;
            text-decoration: none;
            font-size: 18px;
            font-weight: bold;
            margin: 20px 10px;
            transition: all 0.3s ease;
            border: none;
            cursor: pointer;
            box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
        }
        .download-btn:hover {
            transform: translateY(-3px);
            box-shadow: 0 8px 25px rgba(40, 167, 69, 0.4);
        }
        .download-btn:active {
            transform: translateY(-1px);
        }
        .alternative-btn {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            box-shadow: 0 5px 15px rgba(23, 162, 184, 0.3);
        }
        .alternative-btn:hover {
            box-shadow: 0 8px 25px rgba(23, 162, 184, 0.4);
        }
        .back-btn {
            background: linear-gradient(135deg, #6c757d 0%, #495057 100%);
            box-shadow: 0 5px 15px rgba(108, 117, 125, 0.3);
            font-size: 16px;
            padding: 12px 24px;
        }
        .back-btn:hover {
            box-shadow: 0 8px 25px rgba(108, 117, 125, 0.4);
        }
        .tips {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            color: #856404;
            padding: 15px;
            border-radius: 10px;
            margin: 20px 0;
            text-align: left;
            font-size: 14px;
            line-height: 1.5;
        }
        .tips strong {
            display: block;
            margin-bottom: 8px;
            color: #856404;
        }
        .progress {
            display: none;
            margin: 20px 0;
        }
        .progress-bar {
            width: 100%;
            height: 6px;
            background: #e9ecef;
            border-radius: 3px;
            overflow: hidden;
        }
        .progress-fill {
            height: 100%;
            background: linear-gradient(90deg, #28a745, #20c997);
            width: 0%;
            transition: width 0.3s ease;
            animation: progress-animation 2s ease-in-out infinite;
        }
        @keyframes progress-animation {
            0% { width: 0%; }
            50% { width: 70%; }
            100% { width: 100%; }
        }
        .status {
            margin: 15px 0;
            padding: 10px;
            border-radius: 5px;
            display: none;
        }
        .status.success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        .status.error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        @media (max-width: 600px) {
            .container {
                padding: 30px 20px;
                margin: 10px;
            }
            .download-btn {
                display: block;
                margin: 15px 0;
                padding: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📥</div>
        <h1>强制下载PDF文件</h1>
        <p class="subtitle">专为安卓微信环境优化的下载方案</p>
        
        <div class="file-info">
            <h3>📄 文件信息</h3>
            <p><strong>姓名：</strong><?php echo htmlspecialchars($report['name']); ?></p>
            <p><strong>手机号：</strong><?php echo htmlspecialchars($report['phone']); ?></p>
            <p><strong>订单号：</strong><?php echo htmlspecialchars($report['order_number']); ?></p>
            <p><strong>文件名：</strong><?php echo htmlspecialchars($safe_filename); ?></p>
            <p><strong>文件大小：</strong><?php echo round(filesize($pdf_path) / 1024, 2); ?> KB</p>
        </div>
        
        <?php if ($is_wechat && $is_android): ?>
        <div class="tips">
            <strong>🤖 安卓微信用户特别提示：</strong>
            1. 点击下载后，文件通常保存在"下载"文件夹中<br>
            2. 可以使用WPS、Adobe Reader等应用打开PDF<br>
            3. 如果下载失败，请尝试"复制链接"方案
        </div>
        <?php endif; ?>
        
        <div class="progress" id="progressBar">
            <p>正在准备下载...</p>
            <div class="progress-bar">
                <div class="progress-fill"></div>
            </div>
        </div>
        
        <div class="status" id="statusMessage"></div>
        
        <div>
            <a href="?id=<?php echo htmlspecialchars($report_id); ?>&action=download" 
               class="download-btn" 
               id="downloadBtn"
               onclick="startDownload(this)">
                💾 立即下载PDF
            </a>
            
            <a href="android_wechat_solution.php?id=<?php echo htmlspecialchars($report_id); ?>" 
               class="download-btn alternative-btn">
                🔗 其他解决方案
            </a>
        </div>
        
        <div style="margin-top: 30px;">
            <a href="javascript:history.back()" class="download-btn back-btn">
                ← 返回上一页
            </a>
        </div>
    </div>
    
    <script>
        function startDownload(btn) {
            // 显示进度条
            document.getElementById('progressBar').style.display = 'block';
            btn.style.opacity = '0.6';
            btn.style.pointerEvents = 'none';
            
            // 2秒后隐藏进度条并显示成功消息
            setTimeout(function() {
                document.getElementById('progressBar').style.display = 'none';
                showStatus('success', '✅ 下载已开始！请检查您的下载文件夹。');
                btn.style.opacity = '1';
                btn.style.pointerEvents = 'auto';
                btn.textContent = '🔄 重新下载';
            }, 2000);
            
            // 如果是安卓微信，给出额外提示
            const ua = navigator.userAgent;
            if (ua.indexOf('MicroMessenger') > -1 && ua.indexOf('Android') > -1) {
                setTimeout(function() {
                    if (confirm('如果下载没有开始，可能是微信限制了下载功能。\n是否尝试其他解决方案？')) {
                        window.location.href = 'android_wechat_solution.php?id=<?php echo htmlspecialchars($report_id); ?>';
                    }
                }, 3000);
            }
        }
        
        function showStatus(type, message) {
            const statusEl = document.getElementById('statusMessage');
            statusEl.className = 'status ' + type;
            statusEl.textContent = message;
            statusEl.style.display = 'block';
            
            // 5秒后隐藏状态消息
            setTimeout(function() {
                statusEl.style.display = 'none';
            }, 5000);
        }
        
        // 页面加载完成后的检测
        document.addEventListener('DOMContentLoaded', function() {
            const ua = navigator.userAgent;
            
            // 如果是安卓微信，显示特别提示
            if (ua.indexOf('MicroMessenger') > -1 && ua.indexOf('Android') > -1) {
                setTimeout(function() {
                    showStatus('error', '⚠️ 检测到安卓微信环境，下载功能可能受限。建议尝试其他解决方案。');
                }, 1000);
            }
        });
    </script>
</body>
</html>

<?php
} catch (Exception $e) {
    die('查询出错：' . $e->getMessage());
}
?>
