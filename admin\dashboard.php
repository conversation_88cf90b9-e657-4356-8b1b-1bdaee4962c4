<?php
session_start();
require_once '../config/database.php';

// 检查是否已登录
if (!isset($_SESSION['admin_logged_in']) || $_SESSION['admin_logged_in'] !== true) {
    header('Location: login.php');
    exit();
}

$database = new Database();
$conn = $database->getConnection();

$success_message = '';
$error_message = '';

// 处理报告上传
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'add_report') {
    $name = trim($_POST['name'] ?? '');
    $phone = trim($_POST['phone'] ?? '');
    $order_number = trim($_POST['order_number'] ?? '');
    
    if (!empty($name) && !empty($phone) && !empty($order_number) && isset($_FILES['pdf_file'])) {
        $file = $_FILES['pdf_file'];
        
        // 验证文件类型
        if ($file['type'] === 'application/pdf' && $file['error'] === 0) {
            // 创建上传目录
            $upload_dir = '../uploads/reports/';
            if (!is_dir($upload_dir)) {
                mkdir($upload_dir, 0755, true);
            }
            
            // 生成唯一文件名
            $file_extension = '.pdf';
            $new_filename = uniqid() . '_' . time() . $file_extension;
            $upload_path = $upload_dir . $new_filename;
            
            if (move_uploaded_file($file['tmp_name'], $upload_path)) {
                try {
                    $stmt = $conn->prepare("INSERT INTO reports (name, phone, order_number, pdf_path, upload_time) VALUES (?, ?, ?, ?, ?)");
                    $stmt->execute([$name, $phone, $order_number, 'uploads/reports/' . $new_filename, time()]);
                    $success_message = '报告添加成功！';
                } catch (PDOException $e) {
                    if ($e->getCode() == 23000) {
                        $error_message = '订单号已存在，请检查后重新输入！';
                    } else {
                        $error_message = '数据库错误：' . $e->getMessage();
                    }
                    unlink($upload_path); // 删除已上传的文件
                }
            } else {
                $error_message = '文件上传失败！';
            }
        } else {
            $error_message = '请上传有效的PDF文件！';
        }
    } else {
        $error_message = '请填写所有必填字段并上传PDF文件！';
    }
}

// 处理删除报告
if ($_SERVER['REQUEST_METHOD'] === 'POST' && isset($_POST['action']) && $_POST['action'] === 'delete_report') {
    $report_id = $_POST['report_id'] ?? '';
    
    if (!empty($report_id)) {
        // 获取文件信息
        $stmt = $conn->prepare("SELECT pdf_path FROM reports WHERE id = ?");
        $stmt->execute([$report_id]);
        $report = $stmt->fetch(PDO::FETCH_ASSOC);
        
        if ($report) {
            // 删除数据库记录
            $stmt = $conn->prepare("DELETE FROM reports WHERE id = ?");
            $stmt->execute([$report_id]);
            
            // 删除文件
            $file_path = '../' . $report['pdf_path'];
            if (file_exists($file_path)) {
                unlink($file_path);
            }
            
            $success_message = '报告删除成功！';
        }
    }
}

// 获取所有报告
$stmt = $conn->prepare("SELECT * FROM reports ORDER BY upload_time DESC");
$stmt->execute();
$reports = $stmt->fetchAll();
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>管理后台 - 报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: #f8f9fa;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .navbar {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .card {
            border: none;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            transition: all 0.3s ease;
        }
        .card:hover {
            transform: translateY(-5px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .card-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 15px 15px 0 0 !important;
            border: none;
        }
        .btn-primary {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
        }
        .btn-primary:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .form-control {
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        .form-control:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .table {
            border-radius: 10px;
            overflow: hidden;
        }
        .table thead th {
            background: #667eea;
            color: white;
            border: none;
        }
        .btn-sm {
            border-radius: 8px;
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
    </style>
</head>
<body>
    <!-- 导航栏 -->
    <nav class="navbar navbar-expand-lg navbar-dark">
        <div class="container">
            <a class="navbar-brand" href="#">
                <i class="fas fa-file-alt me-2"></i>
                报告管理系统
            </a>
            <div class="navbar-nav ms-auto">
                <span class="navbar-text me-3">
                    <i class="fas fa-user me-1"></i>
                    欢迎，<?php echo htmlspecialchars($_SESSION['admin_username']); ?>
                </span>
                <a class="nav-link" href="orders.php">
                    <i class="fas fa-shopping-cart me-1"></i>
                    订单管理
                </a>
                <a class="nav-link" href="../index.php">
                    <i class="fas fa-search me-1"></i>
                    用户查询页面
                </a>
                <a class="nav-link" href="change_password.php">
                    <i class="fas fa-key me-1"></i>
                    修改密码
                </a>
                <a class="nav-link" href="logout.php">
                    <i class="fas fa-sign-out-alt me-1"></i>
                    退出登录
                </a>
            </div>
        </div>
    </nav>

    <div class="container mt-4">
        <!-- 消息提示 -->
        <?php if ($success_message): ?>
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                <i class="fas fa-check-circle me-2"></i>
                <?php echo htmlspecialchars($success_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>
        
        <?php if ($error_message): ?>
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <i class="fas fa-exclamation-triangle me-2"></i>
                <?php echo htmlspecialchars($error_message); ?>
                <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- 添加报告表单 -->
            <div class="col-lg-4 mb-4">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-plus-circle me-2"></i>
                            添加新报告
                        </h5>
                    </div>
                    <div class="card-body">
                        <form method="POST" enctype="multipart/form-data">
                            <input type="hidden" name="action" value="add_report">
                            
                            <div class="mb-3">
                                <label for="name" class="form-label">
                                    <i class="fas fa-user me-1"></i>
                                    姓名
                                </label>
                                <input type="text" class="form-control" id="name" name="name" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="phone" class="form-label">
                                    <i class="fas fa-phone me-1"></i>
                                    手机号
                                </label>
                                <input type="tel" class="form-control" id="phone" name="phone" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="order_number" class="form-label">
                                    <i class="fas fa-receipt me-1"></i>
                                    订单号
                                </label>
                                <input type="text" class="form-control" id="order_number" name="order_number" required>
                            </div>
                            
                            <div class="mb-3">
                                <label for="pdf_file" class="form-label">
                                    <i class="fas fa-file-pdf me-1"></i>
                                    PDF报告
                                </label>
                                <input type="file" class="form-control" id="pdf_file" name="pdf_file" accept=".pdf" required>
                                <div class="form-text">只支持PDF格式文件</div>
                            </div>
                            
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-upload me-2"></i>
                                上传报告
                            </button>
                        </form>
                    </div>
                </div>
            </div>
            
            <!-- 报告列表 -->
            <div class="col-lg-8">
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">
                            <i class="fas fa-list me-2"></i>
                            报告列表 (共 <?php echo count($reports); ?> 条)
                        </h5>
                    </div>
                    <div class="card-body">
                        <?php if (empty($reports)): ?>
                            <div class="text-center py-5">
                                <i class="fas fa-inbox fa-3x text-muted mb-3"></i>
                                <p class="text-muted">暂无报告数据</p>
                            </div>
                        <?php else: ?>
                            <div class="table-responsive">
                                <table class="table table-hover">
                                    <thead>
                                        <tr>
                                            <th>姓名</th>
                                            <th>手机号</th>
                                            <th>订单号</th>
                                            <th>文件名</th>
                                            <th>上传时间</th>
                                            <th>操作</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                        <?php foreach ($reports as $report): ?>
                                            <tr>
                                                <td><?php echo htmlspecialchars($report['name']); ?></td>
                                                <td><?php echo htmlspecialchars($report['phone']); ?></td>
                                                <td><?php echo htmlspecialchars($report['order_number']); ?></td>
                                                <td>
                                                    <i class="fas fa-file-pdf text-danger me-1"></i>
                                                    <?php echo htmlspecialchars(basename($report['pdf_path'])); ?>
                                                </td>
                                                <td><?php echo date('Y-m-d H:i', $report['upload_time']); ?></td>
                                                <td>
                                                    <a href="../<?php echo htmlspecialchars($report['pdf_path']); ?>" 
                                                       class="btn btn-sm btn-outline-primary me-1" target="_blank">
                                                        <i class="fas fa-eye"></i>
                                                    </a>
                                                    <form method="POST" style="display: inline;" 
                                                          onsubmit="return confirm('确定要删除这个报告吗？')">
                                                        <input type="hidden" name="action" value="delete_report">
                                                        <input type="hidden" name="report_id" value="<?php echo $report['id']; ?>">
                                                        <button type="submit" class="btn btn-sm btn-outline-danger">
                                                            <i class="fas fa-trash"></i>
                                                        </button>
                                                    </form>
                                                </td>
                                            </tr>
                                        <?php endforeach; ?>
                                    </tbody>
                                </table>
                            </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>