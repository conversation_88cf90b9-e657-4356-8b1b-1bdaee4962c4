<?php
require_once 'config/database.php';

$search_result = null;
$error_message = '';
$search_performed = false;

if ($_SERVER['REQUEST_METHOD'] === 'POST') {
    $search_type = $_POST['search_type'] ?? '';
    $search_value = trim($_POST['search_value'] ?? '');
    
    if (!empty($search_value)) {
        $database = new Database();
        $conn = $database->getConnection();
        
        $search_performed = true;
        
        try {
            switch ($search_type) {
                case 'name':
                    $stmt = $conn->prepare("SELECT * FROM reports WHERE name = ?");
                    break;
                case 'phone':
                    $stmt = $conn->prepare("SELECT * FROM reports WHERE phone = ?");
                    break;
                case 'order_number':
                    $stmt = $conn->prepare("SELECT * FROM reports WHERE order_number = ?");
                    break;
                default:
                    throw new Exception('无效的搜索类型');
            }
            
            $stmt->execute([$search_value]);
            $search_result = $stmt->fetchAll();
            
            if (empty($search_result)) {
                $error_message = '未找到相关报告，请检查输入信息是否正确。';
            }
        } catch (Exception $e) {
            $error_message = '查询出错：' . $e->getMessage();
        }
    } else {
        $error_message = '请输入查询内容。';
    }
}
?>

<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>报告查询系统</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <style>
        body {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
        }
        .main-container {
            background: rgba(255, 255, 255, 0.95);
            border-radius: 20px;
            box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.2);
            margin: 2rem auto;
        }
        .header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            border-radius: 20px 20px 0 0;
            padding: 2rem;
            text-align: center;
        }
        .search-section {
            padding: 2rem;
        }
        .form-control, .form-select {
            border-radius: 10px;
            border: 2px solid #e9ecef;
            padding: 12px 15px;
            transition: all 0.3s ease;
        }
        .form-control:focus, .form-select:focus {
            border-color: #667eea;
            box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
        }
        .btn-search {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            border: none;
            border-radius: 10px;
            padding: 12px 30px;
            font-weight: 600;
            transition: all 0.3s ease;
        }
        .btn-search:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
        }
        .result-card {
            background: white;
            border-radius: 15px;
            box-shadow: 0 5px 15px rgba(0,0,0,0.08);
            border: none;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }
        .result-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 10px 25px rgba(0,0,0,0.15);
        }
        .alert {
            border-radius: 10px;
            border: none;
        }
        .btn-download {
            background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
            border: none;
            border-radius: 8px;
        }
        .btn-preview {
            background: linear-gradient(135deg, #17a2b8 0%, #6f42c1 100%);
            border: none;
            border-radius: 8px;
        }

        @media (max-width: 768px) {
            .main-container {
                margin: 1rem;
                border-radius: 15px;
            }
            .header {
                padding: 1.5rem;
                border-radius: 15px 15px 0 0;
            }
            .search-section {
                padding: 1.5rem;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="main-container">
            <div class="header">
                <i class="fas fa-search fa-3x mb-3"></i>
                <h1 class="mb-2">报告查询系统</h1>
                <p class="mb-0">请输入您的姓名、手机号或订单号查询报告</p>
            </div>
            
            <div class="search-section">
                <!-- 搜索表单 -->
                <form method="POST" class="mb-4">
                    <div class="row g-3">
                        <div class="col-md-3">
                            <select class="form-select" name="search_type" required>
                                <option value="">选择查询方式</option>
                                <option value="name" <?php echo (isset($_POST['search_type']) && $_POST['search_type'] === 'name') ? 'selected' : ''; ?>>
                                    <i class="fas fa-user"></i> 姓名
                                </option>
                                <option value="phone" <?php echo (isset($_POST['search_type']) && $_POST['search_type'] === 'phone') ? 'selected' : ''; ?>>
                                    <i class="fas fa-phone"></i> 手机号
                                </option>
                                <option value="order_number" <?php echo (isset($_POST['search_type']) && $_POST['search_type'] === 'order_number') ? 'selected' : ''; ?>>
                                    <i class="fas fa-receipt"></i> 订单号
                                </option>
                            </select>
                        </div>
                        <div class="col-md-6">
                            <input type="text" class="form-control" name="search_value" 
                                   placeholder="请输入查询内容" 
                                   value="<?php echo htmlspecialchars($_POST['search_value'] ?? ''); ?>" required>
                        </div>
                        <div class="col-md-3">
                            <button type="submit" class="btn btn-primary btn-search w-100">
                                <i class="fas fa-search me-2"></i>
                                查询报告
                            </button>
                        </div>
                    </div>
                </form>
                
                <!-- 错误消息 -->
                <?php if ($error_message): ?>
                    <div class="alert alert-warning" role="alert">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <?php echo htmlspecialchars($error_message); ?>
                    </div>
                <?php endif; ?>
                
                <!-- 搜索结果 -->
                <?php if ($search_performed && $search_result && !empty($search_result)): ?>
                    <div class="mt-4">
                        <h4 class="mb-3">
                            <i class="fas fa-file-alt me-2 text-primary"></i>
                            找到 <?php echo count($search_result); ?> 个相关报告
                        </h4>
                        
                        <?php foreach ($search_result as $report): ?>
                            <div class="card result-card">
                                <div class="card-body">
                                    <div class="row align-items-center">
                                        <div class="col-md-8">
                                            <h5 class="card-title mb-2">
                                                <i class="fas fa-file-pdf text-danger me-2"></i>
                                                报告文件
                                            </h5>
                                            <div class="row">
                                                <div class="col-sm-4">
                                                    <small class="text-muted">
                                                        <i class="fas fa-user me-1"></i>
                                                        姓名：<?php echo htmlspecialchars($report['name']); ?>
                                                    </small>
                                                </div>
                                                <div class="col-sm-4">
                                                    <small class="text-muted">
                                                        <i class="fas fa-phone me-1"></i>
                                                        手机：<?php echo htmlspecialchars($report['phone']); ?>
                                                    </small>
                                                </div>
                                                <div class="col-sm-4">
                                                    <small class="text-muted">
                                                        <i class="fas fa-receipt me-1"></i>
                                                        订单：<?php echo htmlspecialchars($report['order_number']); ?>
                                                    </small>
                                                </div>
                                            </div>
                                            <small class="text-muted">
                                                <i class="fas fa-clock me-1"></i>
                                                上传时间：<?php echo date('Y年m月d日 H:i', is_numeric($report['upload_time']) ? $report['upload_time'] : strtotime($report['upload_time'])); ?>
                                            </small>
                                        </div>
                                        <div class="col-md-4 text-end">
                                            <a href="preview.php?id=<?php echo htmlspecialchars($report['id']); ?>" 
                                               class="btn btn-info btn-preview">
                                                <i class="fas fa-eye me-1"></i>
                                                预览报告
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <?php endforeach; ?>
                    </div>
                <?php elseif ($search_performed && empty($search_result)): ?>
                    <div class="text-center py-5">
                        <i class="fas fa-search fa-3x text-muted mb-3"></i>
                        <h5 class="text-muted">未找到相关报告</h5>
                        <p class="text-muted">请检查输入信息是否正确，或联系管理员确认报告是否已上传。</p>
                    </div>
                <?php endif; ?>
                
                <!-- 使用说明 -->
                <?php if (!$search_performed): ?>
                    <div class="mt-5">
                        <div class="row">
                            <div class="col-md-4 text-center mb-3">
                                <div class="p-3">
                                    <i class="fas fa-user-circle fa-3x text-primary mb-3"></i>
                                    <h5>按姓名查询</h5>
                                    <p class="text-muted">输入您的完整姓名进行查询</p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="p-3">
                                    <i class="fas fa-mobile-alt fa-3x text-success mb-3"></i>
                                    <h5>按手机号查询</h5>
                                    <p class="text-muted">输入您的手机号码进行查询</p>
                                </div>
                            </div>
                            <div class="col-md-4 text-center mb-3">
                                <div class="p-3">
                                    <i class="fas fa-receipt fa-3x text-warning mb-3"></i>
                                    <h5>按订单号查询</h5>
                                    <p class="text-muted">输入您的订单编号进行查询</p>
                                </div>
                            </div>
                        </div>
                    </div>
                <?php endif; ?>
            </div>
        </div>
    </div>
    

    
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.1.3/dist/js/bootstrap.bundle.min.js"></script>
    
</body>
</html>